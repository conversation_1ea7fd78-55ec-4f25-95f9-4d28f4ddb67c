# Deployment Instructions for Exporter Application

This guide will help you deploy both the frontend and backend of the Exporter application to production.

## Backend Deployment (Node.js with Firebase)

### Option 1: Deploy to Firebase Cloud Functions

1. Install Firebase CLI globally (if not already installed):
   ```
   npm install -g firebase-tools
   ```

2. Initialize Firebase in your project:
   ```
   cd exporter-be
   firebase init functions
   ```

3. Convert your Express app to a Firebase function by modifying `functions/index.js`:
   ```javascript
   const functions = require('firebase-functions');
   const app = require('./src/server');

   exports.api = functions.https.onRequest(app);
   ```

4. Update your server.js to export the app:
   ```javascript
   // At the end of server.js
   module.exports = app;
   ```

5. Deploy to Firebase:
   ```
   firebase deploy --only functions
   ```

### Option 2: Deploy to a VPS or Cloud Provider (e.g., DigitalOcean, AWS, GCP)

1. Set up a VPS with Node.js installed
2. Clone your repository to the server
3. Install dependencies:
   ```
   cd exporter-be
   npm install --production
   ```

4. Set up environment variables on your server
5. Use PM2 to manage your Node.js process:
   ```
   npm install -g pm2
   pm2 start src/server.js --name exporter-backend
   pm2 save
   pm2 startup
   ```

6. Set up Nginx as a reverse proxy:
   ```
   server {
       listen 80;
       server_name api.yourdomain.com;

       location / {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

7. Set up SSL with Let's Encrypt

## Frontend Deployment (React)

### Option 1: Deploy to Firebase Hosting

1. Build your React app:
   ```
   cd exporter-fe
   npm run build
   ```

2. Initialize Firebase in your project (if not already done):
   ```
   firebase init hosting
   ```

3. Deploy to Firebase Hosting:
   ```
   firebase deploy --only hosting
   ```

### Option 2: Deploy to Netlify or Vercel

1. Create an account on Netlify or Vercel
2. Connect your GitHub repository
3. Configure the build settings:
   - Build command: `cd exporter-fe && npm run build`
   - Publish directory: `exporter-fe/build`
4. Set up environment variables in the Netlify/Vercel dashboard
5. Deploy your site

## Environment Variables

Make sure to set up the following environment variables in your production environment:

### Backend Environment Variables
```
PORT=5000
FIREBASE_PROJECT_ID=exporter-2ccaa
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_DATABASE_URL=https://exporter-2ccaa.firebaseio.com
JWT_SECRET=your-production-jwt-secret
JWT_EXPIRES_IN=1d
```

### Frontend Environment Variables
```
REACT_APP_FIREBASE_API_KEY=your-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=exporter-2ccaa.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=exporter-2ccaa
REACT_APP_FIREBASE_STORAGE_BUCKET=exporter-2ccaa.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=115509805097958993018
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_FIREBASE_MEASUREMENT_ID=your-measurement-id
REACT_APP_API_URL=https://api.yourdomain.com/api
```

## Domain Setup

1. Purchase a domain name if you don't already have one
2. Set up DNS records to point to your hosting provider
3. Configure SSL certificates for both frontend and backend

## Monitoring and Maintenance

1. Set up logging with a service like Loggly or Papertrail
2. Configure monitoring with tools like New Relic or Datadog
3. Set up automated backups for your Firebase data
4. Implement a CI/CD pipeline for automated deployments

## Security Considerations

1. Ensure all API endpoints are properly secured
2. Implement rate limiting to prevent abuse
3. Regularly update dependencies to patch security vulnerabilities
4. Set up proper Firebase security rules
5. Consider implementing CORS restrictions in production
