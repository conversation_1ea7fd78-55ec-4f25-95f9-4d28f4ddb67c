# Testing the Exporter Application

The application is now set up with Firebase authentication and state storage. Follow these instructions to test the functionality:

## Access the Application

1. Open your browser and navigate to:
   - Frontend: http://localhost:3001
   - Backend API health check: http://localhost:5000/api/health

## Testing Authentication

1. When you first open the application, you should see the login screen
2. Click on "Don't have an account? Register" to create a new account
3. Fill in the registration form with:
   - Email: <EMAIL>
   - Password: password123
   - Display Name: Test User
4. Click "Register"
5. After successful registration, you should be redirected to the main application
6. Test logging out by clicking on your profile or a logout button in the header
7. Log back in with the credentials you just created

## Testing Report Creation and Storage

1. After logging in, click "Create New Report"
2. Give your report a name (e.g., "Test Report")
3. Add a page to the report
4. Add some text and image components to the page
5. Make some changes to the components (move them, resize them, edit text)
6. Refresh the page - your changes should be persisted in Firebase
7. Log out and log back in - your reports should still be available

## Checking Firebase Storage

If you have access to the Firebase console:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `exporter-2ccaa`
3. Go to "Authentication" to see your registered user
4. Go to "Firestore Database" to see your stored reports

## Troubleshooting

If you encounter any issues:

1. Check the browser console for frontend errors
2. Check the terminal running the backend server for backend errors
3. Make sure both servers are running
4. Verify that your Firebase configuration is correct in both `.env` files
5. Check that Firebase Authentication and Firestore are properly enabled in your Firebase project

## Next Steps

After confirming that the basic functionality works, you might want to:

1. Implement additional features like report sharing
2. Add more authentication methods (Google, Facebook, etc.)
3. Improve the UI/UX of the authentication screens
4. Add user profile management
5. Implement more advanced Firebase security rules
