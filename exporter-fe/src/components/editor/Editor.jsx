import React, { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import ReportHeader from '../report/ReportHeader';
import Report from '../report/Report';
import PageSettings from '../page/PageSettings';
import { useReport } from '../../hooks/useReport';
import { createTextComponent, createImageComponent } from '../../models/ComponentTypes';
import { generateReportUrl } from '../../utils/urlUtils';
import { useSelector } from 'react-redux';
import './Editor.css';

/**
 * Main editor component that combines all parts
 */
const Editor = () => {
  const navigate = useNavigate();
  const user = useSelector(state => state.auth.user);

  const {
    activeReport,
    activePage,
    updatePage,
    createNewReport,
    addComponent
  } = useReport();

  // Track if URL has been set for this report to prevent unnecessary updates
  const urlSetRef = useRef(null);

  // Update URL when active report changes (but not on every report update)
  useEffect(() => {
    if (activeReport && user) {
      const reportUrl = generateReportUrl(user.uid, activeReport.name, activeReport.id);

      // Only update URL if it's a different report or if URL hasn't been set yet
      if (urlSetRef.current !== activeReport.id) {
        console.log('Setting URL for report:', activeReport.id);
        window.history.replaceState(null, '', reportUrl);
        urlSetRef.current = activeReport.id;
      }
    }
  }, [activeReport?.id, user]); // Only depend on report ID and user, not the entire report object

  // Handle page updates
  const handlePageUpdate = (updates) => {
    if (activeReport && activePage) {
      updatePage(activeReport.id, activePage.id, updates);
    }
  };

  // Handle creating a new report
  const handleCreateReport = async () => {
    try {
      // Use the new parameter to automatically set the report as active
      const newReport = await createNewReport('Untitled Report', true);

      // Navigate to the new report URL
      if (newReport && user) {
        const reportUrl = generateReportUrl(user.uid, newReport.name, newReport.id);
        navigate(reportUrl);
      }
    } catch (error) {
      console.error('Error creating new report:', error);
    }
  };

  // Handle adding a text component
  const handleAddTextComponent = () => {
    if (activeReport && activePage) {
      // Create a text component at the center of the visible area
      const textComponent = createTextComponent(
        `text-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 20    // Y position (20mm from top margin)
      );

      addComponent(activeReport.id, activePage.id, textComponent);
    }
  };

  // Handle adding an image component
  const handleAddImageComponent = () => {
    if (activeReport && activePage) {
      // Create an image component at the center of the visible area
      const imageComponent = createImageComponent(
        `image-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 50    // Y position (50mm from top margin)
      );

      addComponent(activeReport.id, activePage.id, imageComponent);
    }
  };

  return (
    <div className="app-layout">
      {/* Main editor area */}
      <div className="editor-container">
        <ReportHeader />

        {activeReport ? (
          <div className="editor-main">
            <div className="editor-toolbar">
              <div className="tool-group">
                <button
                  className="tool-button"
                  onClick={handleAddTextComponent}
                  disabled={!activePage}
                >
                  Add Text
                </button>
                <button
                  className="tool-button"
                  onClick={handleAddImageComponent}
                  disabled={!activePage}
                >
                  Add Image
                </button>
              </div>
            </div>

            <div className="editor-content">
              <Report />
            </div>

            <div className="editor-sidebar">
              {activePage ? (
                <PageSettings
                  page={activePage}
                  onUpdate={handlePageUpdate}
                />
              ) : (
                <div className="no-page-selected">
                  <p>No page selected</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="no-report-selected">
            <div className="welcome-screen">
              <h1>Welcome to Exporter</h1>
              <p>Create beautiful reports with customizable pages and export them easily.</p>
              <div className="welcome-actions">
                <button
                  className="welcome-button"
                  onClick={handleCreateReport}
                >
                  Create New Report
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Editor;
