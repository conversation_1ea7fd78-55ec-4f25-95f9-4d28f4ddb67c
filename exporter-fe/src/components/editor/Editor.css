/* App layout */
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Editor container */
.editor-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* Ensures the content can shrink properly */
}

/* Toolbar */
.editor-toolbar {
  width: 60px;
  background-color: #444;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tool-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.tool-button {
  width: 100%;
  height: 40px;
  background-color: #555;
  color: white;
  border: none;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: background-color 0.2s;
  cursor: pointer;
}

.tool-button:hover {
  background-color: #666;
}

.tool-button:active {
  background-color: #444;
}

/* Editor content */
.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* Ensures the content can shrink properly */
}

/* Properties sidebar */
.editor-sidebar {
  width: 250px;
  background-color: #f5f5f5;
  border-left: 1px solid #ddd;
  overflow-y: auto;
}

.no-page-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
  font-style: italic;
}

/* Welcome screen and empty state */
.no-report-selected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
}

.welcome-screen {
  text-align: center;
  max-width: 600px;
  padding: 3rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.welcome-screen h1 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 2.5rem;
}

.welcome-screen p {
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.welcome-actions {
  margin-top: 2rem;
}

.welcome-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.welcome-button:hover {
  background-color: #3a7bc8;
}

.empty-state {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.empty-state h2 {
  margin-bottom: 1rem;
  color: #555;
}

.empty-state p {
  color: #888;
  line-height: 1.5;
}
