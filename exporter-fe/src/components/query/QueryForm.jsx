import React, { useState } from 'react';
import { createQuery } from '../../services/queryService';
import { QUERY_TEMPLATES } from 'exporter-shared-utils';
import './QueryForm.css';

const QueryForm = () => {
  const [formData, setFormData] = useState({
    title: '',
    query: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const result = await createQuery(
        formData.title,
        formData.query,
        formData.description
      );
      
      setMessage(`Query "${result.title}" created successfully!`);
      setMessageType('success');
      
      // Reset form
      setFormData({
        title: '',
        query: '',
        description: ''
      });
    } catch (error) {
      setMessage(`Error: ${error.message}`);
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadTemplate = (template) => {
    // Use shared utility templates
    const templateData = QUERY_TEMPLATES[template];
    if (templateData) {
      setFormData({
        title: templateData.title,
        query: templateData.query,
        description: templateData.description
      });
    }
  };

  return (
    <div className="query-form">
      <h2>Create New Query</h2>
      
      {message && (
        <div className={`message ${messageType}`}>
          {message}
        </div>
      )}

      <div className="template-buttons">
        <h3>Quick Templates:</h3>
        <button 
          type="button" 
          onClick={() => loadTemplate('SELECT_ALL')}
          className="template-btn"
        >
          Select All
        </button>
        <button 
          type="button" 
          onClick={() => loadTemplate('COUNT_RECORDS')}
          className="template-btn"
        >
          Count Records
        </button>
        <button 
          type="button" 
          onClick={() => loadTemplate('FILTERED_SELECT')}
          className="template-btn"
        >
          Filtered Select
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="title">Query Title *</label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            required
            placeholder="Enter query title"
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter query description (optional)"
            rows="3"
          />
        </div>

        <div className="form-group">
          <label htmlFor="query">SQL Query *</label>
          <textarea
            id="query"
            name="query"
            value={formData.query}
            onChange={handleInputChange}
            required
            placeholder="Enter your SQL query"
            rows="8"
          />
        </div>

        <button 
          type="submit" 
          disabled={isLoading}
          className="submit-btn"
        >
          {isLoading ? 'Creating...' : 'Create Query'}
        </button>
      </form>
    </div>
  );
};

export default QueryForm;
