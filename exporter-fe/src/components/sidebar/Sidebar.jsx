import React from 'react';
import { useReport } from '../../hooks/useReport';
import './Sidebar.css';

/**
 * Sidebar component for the application
 */
const Sidebar = () => {
  const {
    reports,
    activeReport,
    createNewReport,
    setActiveReport,
    deleteReport
  } = useReport();

  // Create a new report
  const handleCreateReport = () => {
    const newReport = createNewReport();
    setActiveReport(newReport.id);
  };

  // Set the active report
  const handleSelectReport = (reportId) => {
    setActiveReport(reportId);
  };

  // Delete a report
  const handleDeleteReport = (e, reportId) => {
    e.stopPropagation(); // Prevent triggering the select report action
    if (window.confirm('Are you sure you want to delete this report?')) {
      deleteReport(reportId);
    }
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2>Reports</h2>
        <button
          className="add-report-button"
          onClick={handleCreateReport}
          title="Create new report"
        >
          + New Report
        </button>
      </div>

      <div className="reports-list">
        {reports.length === 0 ? (
          <div className="no-reports-message">
            No reports yet. Create your first report!
          </div>
        ) : (
          <ul>
            {reports.map(report => (
              <li
                key={report.id}
                className={activeReport && activeReport.id === report.id ? 'active' : ''}
                onClick={() => handleSelectReport(report.id)}
              >
                <span className="report-name">{report.name}</span>
                <span className="report-date">
                  {new Date(report.updatedAt).toLocaleDateString()}
                </span>
                <button
                  className="delete-report-button"
                  onClick={(e) => handleDeleteReport(e, report.id)}
                  title="Delete report"
                >
                  ×
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
