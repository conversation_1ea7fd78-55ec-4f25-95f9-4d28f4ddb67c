/* Element Settings styles */
.element-settings {
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 4px;
  height: 100%;
  overflow-y: auto;
}

.element-settings h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.5rem;
}

.element-id {
  margin-bottom: 1rem;
  padding: 0.25rem 0.5rem;
  background-color: #e3f2fd;
  border-radius: 3px;
  border-left: 3px solid #2196f3;
}

.element-id small {
  color: #1976d2;
  font-family: monospace;
  font-size: 0.75rem;
}

.settings-group {
  margin-bottom: 1.5rem;
}

.settings-group > label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #555;
  font-size: 0.9rem;
}

.element-type {
  padding: 0.5rem;
  background-color: #e9ecef;
  border-radius: 4px;
  font-weight: 500;
  color: #495057;
  text-transform: capitalize;
}

/* Dimension inputs */
.dimension-inputs,
.position-inputs {
  display: flex;
  gap: 0.75rem;
}

.dimension-input-group,
.position-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dimension-input-group label,
.position-input-group label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #666;
  margin-bottom: 0.25rem;
}

.dimension-input,
.position-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  width: 100%;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.dimension-input:focus,
.position-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dimension-inputs,
  .position-inputs {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .element-settings {
    padding: 0.75rem;
  }
}

/* Input validation states */
.dimension-input:invalid,
.position-input:invalid {
  border-color: #dc3545;
}

.dimension-input:invalid:focus,
.position-input:invalid:focus {
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}
