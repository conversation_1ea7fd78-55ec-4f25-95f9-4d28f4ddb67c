import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import DraggableComponent from './DraggableComponent';
import { storeImageData, getImageData } from '../../utils/storageUtils';
import './ImageComponent.css';

/**
 * Image component for the page
 */
const ImageComponent = ({
  component,
  zoom,
  isSelected,
  pageMargins,
  pageDimensions,
  onSelect,
  onUpdate
}) => {
  // Ref for file input
  const fileInputRef = useRef(null);
  // State for image source
  const [imageSrc, setImageSrc] = useState(component.src);

  // Load image from storage if needed
  useEffect(() => {
    if (component.src === 'image-data-removed-for-storage' && component.id) {
      // Try to load from storage
      const storedImage = getImageData(component.id);
      if (storedImage) {
        setImageSrc(storedImage);
      }
    } else {
      setImageSrc(component.src);
    }
  }, [component.id, component.src]);

  // Handle image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const dataUrl = event.target.result;

      // Store the image data separately
      if (storeImageData(component.id, dataUrl)) {
        // Update component with a reference to the stored image
        onUpdate({
          src: dataUrl, // Keep the actual data URL in memory for the current session
          alt: file.name
        });

        // Update local state for immediate display
        setImageSrc(dataUrl);
      } else {
        // If storage failed, still try to use the image in memory
        onUpdate({
          src: dataUrl,
          alt: file.name
        });
        setImageSrc(dataUrl);
        console.warn('Image storage failed, using in-memory only');
      }
    };
    reader.readAsDataURL(file);
  };

  // Handle click on upload button
  const handleUploadClick = (e) => {
    e.stopPropagation();
    fileInputRef.current.click();
  };

  // Image style
  const imageStyle = {
    objectFit: component.objectFit,
    borderWidth: `${component.border.width}px`,
    borderStyle: component.border.style,
    borderColor: component.border.color
  };

  return (
    <DraggableComponent
      component={component}
      zoom={zoom}
      isSelected={isSelected}
      pageMargins={pageMargins}
      pageDimensions={pageDimensions}
      onSelect={onSelect}
      onUpdate={onUpdate}
    >
      {imageSrc ? (
        <img
          src={imageSrc}
          alt={component.alt}
          className="image-component"
          style={imageStyle}
        />
      ) : (
        <div className="image-placeholder" onClick={handleUploadClick}>
          <span>Click to add image</span>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            style={{ display: 'none' }}
          />
        </div>
      )}
    </DraggableComponent>
  );
};

ImageComponent.propTypes = {
  component: PropTypes.object.isRequired,
  zoom: PropTypes.number.isRequired,
  isSelected: PropTypes.bool,
  pageMargins: PropTypes.object.isRequired,
  pageDimensions: PropTypes.object.isRequired,
  onSelect: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired
};

ImageComponent.defaultProps = {
  isSelected: false
};

export default ImageComponent;
