/* Page wrapper styles */
.page-wrapper {
  display: inline-block;
  position: relative;
  margin: 0; /* No margin as we're using gap in the container */
  max-width: none; /* Allow the page to exceed container width */
  min-width: min-content; /* Ensure the page takes at least its content width */
  cursor: pointer; /* Make it clear that pages are clickable */
}

/* Page component styles */
.page {
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  transition: width 0.3s ease, height 0.3s ease;
  margin: 0;
  cursor: default; /* Default cursor for the page */
  min-width: min-content; /* Ensure page takes at least its content width */
  max-width: none; /* Allow page to exceed container width */
}

.page-wrapper.active .page {
  box-shadow: 0 0 0 3px #4a90e2, 0 0 15px rgba(0, 0, 0, 0.4);
}

/* Active page indicator */
.page-wrapper.active::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #4a90e2;
  z-index: 1;
}

.page-content {
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden; /* Prevent components from overflowing */
}

.empty-page-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #aaa;
  font-style: italic;
  user-select: none;
  cursor: default; /* Default cursor for empty page message */
}

/* Hide empty page message when not active */
.page:not(.active) .empty-page-message {
  opacity: 0;
}
