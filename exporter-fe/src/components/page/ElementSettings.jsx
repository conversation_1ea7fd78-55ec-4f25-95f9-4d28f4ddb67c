import React from 'react';
import PropTypes from 'prop-types';
import './ElementSettings.css';

/**
 * Component for editing element settings
 */
const ElementSettings = ({ component, onUpdate }) => {
  // Handle width change
  const handleWidthChange = (e) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value > 0) {
      onUpdate({ width: value });
    }
  };

  // Handle height change
  const handleHeightChange = (e) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value > 0) {
      onUpdate({ height: value });
    }
  };

  // Handle X position change
  const handleXChange = (e) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      onUpdate({ x: value });
    }
  };

  // Handle Y position change
  const handleYChange = (e) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      onUpdate({ y: value });
    }
  };

  return (
    <div className="element-settings">
      <h3>Element Settings</h3>
      <div className="element-id">
        <small>ID: {component.id}</small>
      </div>
      
      <div className="settings-group">
        <label>Type</label>
        <div className="element-type">
          {component.type.charAt(0).toUpperCase() + component.type.slice(1)}
        </div>
      </div>

      <div className="settings-group">
        <label>Dimensions (mm)</label>
        <div className="dimension-inputs">
          <div className="dimension-input-group">
            <label>Width:</label>
            <input
              type="number"
              min="1"
              max="1000"
              step="0.1"
              value={component.width}
              onChange={handleWidthChange}
              className="dimension-input"
            />
          </div>
          <div className="dimension-input-group">
            <label>Height:</label>
            <input
              type="number"
              min="1"
              max="1000"
              step="0.1"
              value={component.height}
              onChange={handleHeightChange}
              className="dimension-input"
            />
          </div>
        </div>
      </div>

      <div className="settings-group">
        <label>Position (mm)</label>
        <div className="position-inputs">
          <div className="position-input-group">
            <label>X:</label>
            <input
              type="number"
              min="0"
              max="1000"
              step="0.1"
              value={component.x}
              onChange={handleXChange}
              className="position-input"
            />
          </div>
          <div className="position-input-group">
            <label>Y:</label>
            <input
              type="number"
              min="0"
              max="1000"
              step="0.1"
              value={component.y}
              onChange={handleYChange}
              className="position-input"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

ElementSettings.propTypes = {
  component: PropTypes.object.isRequired,
  onUpdate: PropTypes.func.isRequired
};

export default ElementSettings;
