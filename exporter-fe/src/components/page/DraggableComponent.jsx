import React, { useRef, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { mmToPx, pxToMm } from '../../utils/unitConversion';
import './DraggableComponent.css';

/**
 * Base component for draggable elements on a page
 */
const DraggableComponent = ({
  component,
  zoom,
  isSelected,
  pageMargins,
  pageDimensions,
  onSelect,
  onUpdate,
  onDoubleClick,
  disableDrag = false, // Prop to disable dragging
  children
}) => {
  // Refs for drag and resize handling
  const componentRef = useRef(null);
  const dragStartRef = useRef({ x: 0, y: 0 });
  const originalPositionRef = useRef({ x: 0, y: 0 });
  const resizeStartRef = useRef({ x: 0, y: 0, width: 0, height: 0 });

  // State for drag and resize operations
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null);

  // Convert mm positions to pixels with zoom factor
  const xPx = mmToPx(component.x) * (zoom / 100);
  const yPx = mmToPx(component.y) * (zoom / 100);
  const widthPx = mmToPx(component.width) * (zoom / 100);
  const heightPx = mmToPx(component.height) * (zoom / 100);

  // Determine cursor based on state
  // Only set cursor for the container if we're dragging or resizing
  // Otherwise, let child components set their own cursor
  let cursor = 'auto'; // Default cursor is auto to allow children to set their own
  if (isDragging) {
    cursor = 'grabbing';
  } else if (isResizing) {
    // Set cursor based on which handle is being used
    switch (resizeHandle) {
      case 'top-left': cursor = 'nwse-resize'; break;
      case 'top-right': cursor = 'nesw-resize'; break;
      case 'bottom-left': cursor = 'nesw-resize'; break;
      case 'bottom-right': cursor = 'nwse-resize'; break;
      default: cursor = 'auto';
    }
  }

  // Component style
  const componentStyle = {
    left: `${xPx}px`,
    top: `${yPx}px`,
    width: `${widthPx}px`,
    height: `${heightPx}px`,
    zIndex: isSelected ? 10 : 1
    // Removed cursor style to allow child components to set their own
  };

  // State to track if mouse is over the border
  const [isOverBorder, setIsOverBorder] = useState(false);

  // Handle mouse down to start dragging
  const handleMouseDown = (e) => {
    if (component.locked || disableDrag) return;

    // Only allow dragging when mouse is over the border or not selected
    if (!isSelected || isOverBorder) {
      // Prevent default behavior and propagation
      e.preventDefault();
      e.stopPropagation();

      // Select this component
      onSelect(component.id);

      // Set up drag operation
      dragStartRef.current = { x: e.clientX, y: e.clientY };
      originalPositionRef.current = { x: component.x, y: component.y };

      // Set dragging state - this will trigger the useEffect to add event listeners
      setIsDragging(true);

      // Set cursor to grabbing only if we're actually dragging
      if (isOverBorder) {
        document.body.style.cursor = 'grabbing';
      }
    } else {
      // Just select the component if not over border
      onSelect(component.id);
    }
  };

  // Handle mouse enter on component border
  const handleBorderMouseEnter = () => {
    if (!disableDrag) {
      setIsOverBorder(true);
      // Don't set cursor here, let the child component handle it
    }
  };

  // Handle mouse leave on component border
  const handleBorderMouseLeave = () => {
    setIsOverBorder(false);
    // Don't set cursor here, let the child component handle it
  };

  // Create memoized event handlers that won't change on re-renders
  const handleMouseMove = React.useCallback((e) => {
    // Use requestAnimationFrame for smoother dragging and resizing
    requestAnimationFrame(() => {
      if (isDragging) {
        // Calculate the delta in pixels
        const deltaX = e.clientX - dragStartRef.current.x;
        const deltaY = e.clientY - dragStartRef.current.y;

        // Convert delta to mm based on zoom level
        const deltaXmm = deltaX / (mmToPx(1) * (zoom / 100));
        const deltaYmm = deltaY / (mmToPx(1) * (zoom / 100));

        // Calculate new position
        const newX = originalPositionRef.current.x + deltaXmm;
        const newY = originalPositionRef.current.y + deltaYmm;

        // Constrain to page boundaries with proper margin handling
        const minX = pageMargins.left;
        const minY = pageMargins.top;
        const maxX = pageDimensions.width - pageMargins.right - component.width;
        const maxY = pageDimensions.height - pageMargins.bottom - component.height;

        // Boundaries are calculated above

        // Ensure we respect all margins - use strict enforcement
        let constrainedX = Math.min(maxX, Math.max(minX, newX));
        let constrainedY = Math.min(maxY, Math.max(minY, newY));

        // Strict enforcement of bottom and right margins
        const rightBoundary = pageDimensions.width - pageMargins.right;
        const bottomBoundary = pageDimensions.height - pageMargins.bottom;

        if (constrainedX + component.width > rightBoundary) {
          constrainedX = rightBoundary - component.width;
        }

        if (constrainedY + component.height > bottomBoundary) {
          constrainedY = bottomBoundary - component.height;
        }

        // Update component position
        onUpdate({
          x: constrainedX,
          y: constrainedY
        });
      } else if (isResizing) {
        // Calculate the delta in pixels
        const deltaX = e.clientX - resizeStartRef.current.x;
        const deltaY = e.clientY - resizeStartRef.current.y;

        // Convert delta to mm based on zoom level
        const deltaXmm = deltaX / (mmToPx(1) * (zoom / 100));
        const deltaYmm = deltaY / (mmToPx(1) * (zoom / 100));

        // Calculate new dimensions based on which handle is being used
        let newWidth = resizeStartRef.current.width;
        let newHeight = resizeStartRef.current.height;
        let newX = component.x;
        let newY = component.y;

        switch (resizeHandle) {
          case 'bottom-right':
            newWidth = resizeStartRef.current.width + deltaXmm;
            newHeight = resizeStartRef.current.height + deltaYmm;
            break;
          case 'bottom-left':
            newWidth = resizeStartRef.current.width - deltaXmm;
            newHeight = resizeStartRef.current.height + deltaYmm;
            newX = originalPositionRef.current.x + deltaXmm;
            break;
          case 'top-right':
            newWidth = resizeStartRef.current.width + deltaXmm;
            newHeight = resizeStartRef.current.height - deltaYmm;
            newY = originalPositionRef.current.y + deltaYmm;
            break;
          case 'top-left':
            newWidth = resizeStartRef.current.width - deltaXmm;
            newHeight = resizeStartRef.current.height - deltaYmm;
            newX = originalPositionRef.current.x + deltaXmm;
            newY = originalPositionRef.current.y + deltaYmm;
            break;
          default:
            break;
        }

        // Enforce minimum size
        const minSize = 10; // Minimum size in mm
        newWidth = Math.max(minSize, newWidth);
        newHeight = Math.max(minSize, newHeight);

        // Constrain to page boundaries
        const minX = pageMargins.left;
        const minY = pageMargins.top;
        const maxX = pageDimensions.width - pageMargins.right;
        const maxY = pageDimensions.height - pageMargins.bottom;

        // Resize constraints are calculated above

        // Adjust position if needed to keep within boundaries
        if (newX < minX) {
          newWidth = newWidth - (minX - newX);
          newX = minX;
        }
        if (newY < minY) {
          newHeight = newHeight - (minY - newY);
          newY = minY;
        }

        // Strict enforcement of right and bottom margins
        const rightEdge = newX + newWidth;
        const bottomEdge = newY + newHeight;

        if (rightEdge > maxX) {
          // If we're past the right margin, reduce width
          newWidth = maxX - newX;
        }

        if (bottomEdge > maxY) {
          // If we're past the bottom margin, reduce height
          newHeight = maxY - newY;
        }

        // Final safety check
        if (newX + newWidth > maxX) {
          newWidth = maxX - newX;
        }

        if (newY + newHeight > maxY) {
          newHeight = maxY - newY;
        }

        // Update component dimensions and position
        onUpdate({
          width: newWidth,
          height: newHeight,
          x: newX,
          y: newY
        });
      }
    });
  }, [
    isDragging,
    isResizing,
    resizeHandle,
    component.width,
    component.height,
    component.x,
    component.y,
    onUpdate,
    pageMargins.left,
    pageMargins.top,
    pageMargins.right,
    pageMargins.bottom,
    pageDimensions.width,
    pageDimensions.height,
    zoom
  ]);

  // Handle mouse up to end dragging or resizing
  const handleMouseUp = React.useCallback(() => {
    const wasDragging = isDragging;
    const wasResizing = isResizing;

    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle(null);

    // Reset cursor only if we were dragging or resizing
    if (wasDragging || wasResizing) {
      document.body.style.cursor = '';
    }
  }, [isDragging, isResizing]);

  // Add and remove event listeners when dragging or resizing state changes
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Reset cursor when component unmounts
  useEffect(() => {
    return () => {
      // Reset cursor when component unmounts
      if (isDragging || isResizing) {
        document.body.style.cursor = '';
      }
    };
  }, [isDragging, isResizing]);

  // Handle resize start
  const handleResizeStart = (e, handle) => {
    if (component.locked) return;

    // Prevent default behavior and propagation
    e.preventDefault();
    e.stopPropagation();

    // Select this component if not already selected
    onSelect(component.id);

    // Set up resize operation
    setIsResizing(true);
    setResizeHandle(handle);
    resizeStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      width: component.width,
      height: component.height
    };
    originalPositionRef.current = { x: component.x, y: component.y };

    // Set appropriate cursor based on the handle
    let bodyCursor;
    switch (handle) {
      case 'top-left': bodyCursor = 'nwse-resize'; break;
      case 'top-right': bodyCursor = 'nesw-resize'; break;
      case 'bottom-left': bodyCursor = 'nesw-resize'; break;
      case 'bottom-right': bodyCursor = 'nwse-resize'; break;
      default: bodyCursor = 'default';
    }
    // Only set the cursor if we're actually resizing
    document.body.style.cursor = bodyCursor;
  };

  return (
    <div
      ref={componentRef}
      className={`draggable-component ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''} ${isResizing ? 'resizing' : ''} ${disableDrag ? 'editing' : ''}`}
      style={componentStyle}
      onMouseDown={handleMouseDown}
      onClick={(e) => {
        if (!disableDrag) {
          console.log('DraggableComponent - onClick called for component:', component.id);
          e.stopPropagation();
          onSelect(component.id);
        }
      }}
      onDoubleClick={(e) => {
        if (!disableDrag && onDoubleClick) {
          e.stopPropagation();
          onDoubleClick();
        }
      }}
    >
      {/* Draggable border element */}
      <div
        className="component-border"
        onMouseEnter={handleBorderMouseEnter}
        onMouseLeave={handleBorderMouseLeave}
      />

      {children}

      {/* Resize handles (only shown when selected) */}
      {isSelected && !component.locked && !disableDrag && (
        <div className="resize-handles">
          <div
            className="resize-handle top-left"
            onMouseDown={(e) => handleResizeStart(e, 'top-left')}
            style={{ cursor: 'nwse-resize' }}
          />
          <div
            className="resize-handle top-right"
            onMouseDown={(e) => handleResizeStart(e, 'top-right')}
            style={{ cursor: 'nesw-resize' }}
          />
          <div
            className="resize-handle bottom-left"
            onMouseDown={(e) => handleResizeStart(e, 'bottom-left')}
            style={{ cursor: 'nesw-resize' }}
          />
          <div
            className="resize-handle bottom-right"
            onMouseDown={(e) => handleResizeStart(e, 'bottom-right')}
            style={{ cursor: 'nwse-resize' }}
          />
        </div>
      )}
    </div>
  );
};

DraggableComponent.propTypes = {
  component: PropTypes.object.isRequired,
  zoom: PropTypes.number.isRequired,
  isSelected: PropTypes.bool,
  pageMargins: PropTypes.object.isRequired,
  pageDimensions: PropTypes.object.isRequired,
  onSelect: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onDoubleClick: PropTypes.func,
  disableDrag: PropTypes.bool,
  children: PropTypes.node
};

DraggableComponent.defaultProps = {
  isSelected: false,
  disableDrag: false
};

export default DraggableComponent;
