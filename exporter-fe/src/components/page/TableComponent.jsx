import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
} from '@tanstack/react-table';
import DraggableComponent from './DraggableComponent';
import './TableComponent.css';

/**
 * Table component for the page using TanStack Table
 * - Draggable using the DraggableComponent wrapper
 * - Renders data in a table format
 * - Configurable styling and layout
 */
const TableComponent = ({
  component,
  zoom,
  isSelected,
  pageMargins,
  pageDimensions,
  onSelect,
  onUpdate
}) => {
  // Prepare columns for TanStack Table
  const columns = useMemo(() => {
    return component.columns.map(col => ({
      id: col.id,
      header: col.header,
      accessorKey: col.accessorKey,
    }));
  }, [component.columns]);

  // Create table instance
  const table = useReactTable({
    data: component.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Calculate table styles based on component properties
  const tableStyle = {
    width: '100%',
    height: '100%',
    fontSize: `${component.fontSize}pt`,
    fontFamily: component.fontFamily,
    color: component.textColor,
    borderCollapse: 'collapse',
    border: component.showBorder ? `1px solid ${component.borderColor}` : 'none'
  };

  const headerStyle = {
    backgroundColor: component.headerBackgroundColor,
    fontWeight: 'bold',
    borderBottom: component.showBorder ? `1px solid ${component.borderColor}` : 'none'
  };

  const cellStyle = {
    padding: '4px 8px',
    border: component.showBorder ? `1px solid ${component.borderColor}` : 'none',
    textAlign: 'left'
  };

  const getRowStyle = (index) => ({
    backgroundColor: component.alternateRowColors && index % 2 === 1 
      ? '#f9f9f9' 
      : 'transparent'
  });

  return (
    <DraggableComponent
      component={component}
      zoom={zoom}
      isSelected={isSelected}
      pageMargins={pageMargins}
      pageDimensions={pageDimensions}
      onSelect={onSelect}
      onUpdate={onUpdate}
    >
      <div className="table-component-container">
        <table style={tableStyle}>
          {component.showHeader && (
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id} style={headerStyle}>
                  {headerGroup.headers.map(header => (
                    <th key={header.id} style={cellStyle}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
          )}
          <tbody>
            {table.getRowModel().rows.map((row, index) => (
              <tr key={row.id} style={getRowStyle(index)}>
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} style={cellStyle}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </DraggableComponent>
  );
};

TableComponent.propTypes = {
  component: PropTypes.object.isRequired,
  zoom: PropTypes.number.isRequired,
  isSelected: PropTypes.bool,
  pageMargins: PropTypes.object.isRequired,
  pageDimensions: PropTypes.object.isRequired,
  onSelect: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired
};

TableComponent.defaultProps = {
  isSelected: false
};

export default TableComponent;
