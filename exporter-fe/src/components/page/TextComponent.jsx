import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import DraggableComponent from './DraggableComponent';
import RichTextEditor from './RichTextEditor';
import './TextComponent.css';

/**
 * Simplified Text component for the page
 * - Draggable using the DraggableComponent wrapper
 * - Editable on single click when selected
 * - Shows text cursor on hover
 */
const TextComponent = ({
  component,
  zoom,
  isSelected,
  pageMargins,
  pageDimensions,
  onSelect,
  onUpdate
}) => {
  // State to track if we're in edit mode
  const [isEditing, setIsEditing] = useState(false);
  // Reference to the component container for positioning the toolbar
  const componentRef = useRef(null);
  // State to track toolbar position
  const [toolbarPosition, setToolbarPosition] = useState({ x: 0, y: 0 });

  // Text style
  const textStyle = {
    fontFamily: component.fontFamily,
    fontSize: `${component.fontSize}pt`,
    fontWeight: component.fontWeight,
    fontStyle: component.fontStyle,
    textAlign: component.textAlign,
    color: component.color,
    backgroundColor: component.backgroundColor
  };

  // Position the toolbar when entering edit mode
  useEffect(() => {
    if (isEditing && componentRef.current) {
      // Small delay to ensure the DOM has updated
      setTimeout(() => {
        // Position the toolbar above the text component
        const rect = componentRef.current.getBoundingClientRect();
        // Position the toolbar at the top of the component, accounting for scroll
        setToolbarPosition({
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY - 10 // Add a small offset to position above the component
        });
      }, 10);
    }
  }, [isEditing]);

  // Update toolbar position when component moves or resizes
  useEffect(() => {
    if (isEditing && componentRef.current) {
      const rect = componentRef.current.getBoundingClientRect();
      setToolbarPosition({
        x: rect.left + window.scrollX,
        y: rect.top + window.scrollY - 10
      });
    }
  }, [component.x, component.y, component.width, component.height, isEditing, zoom]);

  // Handle click on the text component
  const handleTextClick = (e) => {
    e.stopPropagation();

    // Just select the component on single click
    onSelect(component.id);
  };

  // Handle double click on the text component
  const handleTextDoubleClick = (e) => {
    e.stopPropagation();

    // Select the component and enter edit mode
    onSelect(component.id);
    setIsEditing(true);

    // Set a small delay to ensure the DOM has updated before focusing
    setTimeout(() => {
      // Try to focus the editor
      const editorElement = document.querySelector('.editor-input');
      if (editorElement) {
        editorElement.focus();
      }
    }, 50);
  };

  // Handle blur to exit edit mode
  const handleBlur = (e) => {
    // Check if the related target is part of the formatting toolbar
    // If it is, don't exit edit mode
    const relatedTarget = e.relatedTarget;
    if (relatedTarget && relatedTarget.closest('.formatting-toolbar')) {
      // If the blur is caused by clicking on the toolbar, don't exit edit mode
      return;
    }

    setIsEditing(false);
  };

  // Handle key press in the textarea
  const handleKeyDown = (e) => {
    // Exit edit mode on Escape key
    if (e.key === 'Escape') {
      setIsEditing(false);
      e.preventDefault(); // Prevent the escape key from bubbling up
    }
  };

  // Handle direct click on the draggable component
  const handleComponentClick = (componentId) => {
    // Just select the component, don't enter edit mode
    console.log('TextComponent - handleComponentClick called with:', componentId);
    onSelect(componentId);
  };

  // Handle double click on the draggable component
  const handleComponentDoubleClick = () => {
    // Select the component and enter edit mode
    onSelect(component.id);
    setIsEditing(true);
  };

  return (
    <DraggableComponent
      component={component}
      zoom={zoom}
      isSelected={isSelected}
      pageMargins={pageMargins}
      pageDimensions={pageDimensions}
      onSelect={handleComponentClick}
      onUpdate={onUpdate}
      onDoubleClick={handleComponentDoubleClick}
      disableDrag={isEditing} // Disable dragging when in edit mode
    >
      <div className="text-component-container" ref={componentRef}>
        {isSelected && isEditing ? (
          <>
            {/* Rich Text Editor with integrated toolbar */}
            <RichTextEditor
              content={component.text}
              textStyle={textStyle}
              toolbarPosition={toolbarPosition}
              editorState={component.editorState}
              onChange={(editorState, plainText, htmlContent) => {
                const updates = {};

                // Update editor state and content if available
                if (editorState) {
                  updates.editorState = editorState;
                }

                if (plainText !== null) {
                  updates.text = plainText;
                }

                if (htmlContent) {
                  updates.htmlContent = htmlContent;
                }

                onUpdate(updates);
              }}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              onFocus={() => {
                // Update toolbar position when editor gets focus
                if (componentRef.current) {
                  const rect = componentRef.current.getBoundingClientRect();
                  setToolbarPosition({
                    x: rect.left + window.scrollX,
                    y: rect.top + window.scrollY - 10
                  });
                }
              }}
            />
          </>
        ) : (
          <div
            className="text-component-display"
            style={{
              ...textStyle,
              cursor: 'text' // Always show text cursor on hover
            }}
            onClick={handleTextClick}
            onDoubleClick={handleTextDoubleClick}
          >
            {component.htmlContent ? (
              <div
                className="formatted-content"
                dangerouslySetInnerHTML={{ __html: component.htmlContent }}
              />
            ) : (
              component.text
            )}
          </div>
        )}
      </div>
    </DraggableComponent>
  );
};

TextComponent.propTypes = {
  component: PropTypes.object.isRequired,
  zoom: PropTypes.number.isRequired,
  isSelected: PropTypes.bool,
  pageMargins: PropTypes.object.isRequired,
  pageDimensions: PropTypes.object.isRequired,
  onSelect: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired
};

TextComponent.defaultProps = {
  isSelected: false
};

export default TextComponent;