import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getRoot, $createParagraphNode, $createTextNode } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { FORMAT_TEXT_COMMAND, SELECTION_CHANGE_COMMAND } from 'lexical';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { $isTextNode } from 'lexical';
import './RichTextEditor.css';
import './RichTextToolbar.css';

// Component to update editor with external changes
const EditorUpdater = ({ content, textStyle, editorState }) => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // If we have a saved editor state, try to use it
    if (editorState) {
      try {
        const parsedEditorState = JSON.parse(editorState);
        editor.setEditorState(editor.parseEditorState(parsedEditorState));
        return;
      } catch (error) {
        console.error('Error parsing editor state:', error);
        // Fall back to plain text if parsing fails
      }
    }

    // If no editor state or parsing failed, create a plain text node
    editor.update(() => {
      const root = $getRoot();
      // Clear the editor
      root.clear();

      // Create a new paragraph with the text content
      const paragraph = $createParagraphNode();
      const textNode = $createTextNode(content || '');
      paragraph.append(textNode);
      root.append(paragraph);
    });
  }, [editor, content, editorState]);

  return null;
};

EditorUpdater.propTypes = {
  content: PropTypes.string,
  textStyle: PropTypes.object.isRequired,
  editorState: PropTypes.string
};

EditorUpdater.defaultProps = {
  content: '',
  editorState: null
};

// Toolbar component for the Lexical rich text editor
const RichTextToolbar = ({ position, textStyle, onFormatChange }) => {
  const [editor] = useLexicalComposerContext();
  const [hasSelection, setHasSelection] = useState(false);

  // Listen for selection changes
  useEffect(() => {
    return editor.registerCommand(
      SELECTION_CHANGE_COMMAND,
      () => {
        const selection = $getSelection();
        setHasSelection($isRangeSelection(selection) && !selection.isCollapsed());
        return false;
      },
      1
    );
  }, [editor]);

  // Prevent blur when clicking on toolbar buttons
  const handleMouseDown = (e) => {
    e.preventDefault();
  };

  // Handle bold toggle
  const handleBoldClick = () => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');

    // We don't need to update the entire text style anymore
    // The formatting is applied only to the selected text by Lexical
  };

  // Handle italic toggle
  const handleItalicClick = () => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');

    // We don't need to update the entire text style anymore
    // The formatting is applied only to the selected text by Lexical
  };

  // Handle text alignment
  const handleAlignClick = (alignment) => {
    // For simplicity, we're not implementing text alignment in this example
    // This would require additional Lexical plugins and configuration
    console.log('Align', alignment);
  };

  // Handle font size change
  const handleFontSizeChange = (change) => {
    // For simplicity, we're not implementing font size change in this example
    // This would require additional Lexical plugins and configuration
    console.log('Font size change', change);
  };

  // Handle text color change
  const handleColorChange = (e) => {
    // For simplicity, we're not implementing text color change in this example
    // This would require additional Lexical plugins and configuration
    console.log('Color change', e.target.value);
  };

  // Toolbar style based on position
  const toolbarStyle = {
    left: `${position.x}px`,
    top: `${position.y}px`
  };

  return (
    <div
      className="rich-text-toolbar"
      style={toolbarStyle}
      onMouseDown={handleMouseDown}
    >
      <button
        className={`format-button ${textStyle.fontWeight === 'bold' ? 'active' : ''}`}
        onClick={handleBoldClick}
        title="Bold"
        onMouseDown={handleMouseDown}
        disabled={!hasSelection}
      >
        <strong>B</strong>
      </button>
      <button
        className={`format-button ${textStyle.fontStyle === 'italic' ? 'active' : ''}`}
        onClick={handleItalicClick}
        title="Italic"
        onMouseDown={handleMouseDown}
        disabled={!hasSelection}
      >
        <em>I</em>
      </button>
      <div className="format-divider"></div>
      <button
        className={`format-button ${textStyle.textAlign === 'left' ? 'active' : ''}`}
        onClick={() => handleAlignClick('left')}
        title="Align Left"
        onMouseDown={handleMouseDown}
      >
        <span style={{ fontSize: '16px' }}>&#8592;</span>
      </button>
      <button
        className={`format-button ${textStyle.textAlign === 'center' ? 'active' : ''}`}
        onClick={() => handleAlignClick('center')}
        title="Align Center"
        onMouseDown={handleMouseDown}
      >
        <span style={{ fontSize: '16px' }}>&#8596;</span>
      </button>
      <button
        className={`format-button ${textStyle.textAlign === 'right' ? 'active' : ''}`}
        onClick={() => handleAlignClick('right')}
        title="Align Right"
        onMouseDown={handleMouseDown}
      >
        <span style={{ fontSize: '16px' }}>&#8594;</span>
      </button>
      <div className="format-divider"></div>
      <button
        className="format-button"
        onClick={() => handleFontSizeChange(-1)}
        title="Decrease Font Size"
        onMouseDown={handleMouseDown}
      >
        <span style={{ fontSize: '12px' }}>A-</span>
      </button>
      <span className="font-size-display">{parseInt(textStyle.fontSize, 10)}pt</span>
      <button
        className="format-button"
        onClick={() => handleFontSizeChange(1)}
        title="Increase Font Size"
        onMouseDown={handleMouseDown}
      >
        <span style={{ fontSize: '14px' }}>A+</span>
      </button>
      <div className="format-divider"></div>
      <input
        type="color"
        className="color-picker"
        value={textStyle.color}
        onChange={handleColorChange}
        title="Text Color"
        onMouseDown={handleMouseDown}
      />
    </div>
  );
};

// Main rich text editor component
const RichTextEditor = ({
  content,
  textStyle,
  onChange,
  onBlur,
  onFocus,
  onKeyDown,
  toolbarPosition,
  editorState
}) => {
  // Initial editor configuration
  const initialConfig = {
    namespace: 'RichTextEditor',
    theme: {
      root: 'rich-text-editor',
      text: {
        bold: 'rich-text-editor-bold',
        italic: 'rich-text-editor-italic',
      },
    },
    onError: (error) => console.error(error),
  };

  // Custom function to generate HTML from the editor state
  const generateHtmlFromEditorState = (editorState) => {
    return editorState.read(() => {
      const root = $getRoot();
      let html = '<div>';

      // Process each paragraph
      root.getChildren().forEach(paragraph => {
        html += '<p>';

        // Process each text node in the paragraph
        paragraph.getChildren().forEach(node => {
          if ($isTextNode(node)) {
            let text = node.getTextContent();

            // Skip empty nodes
            if (!text) return;

            // Apply formatting based on node's format
            const format = node.getFormat();

            // Apply bold formatting
            if (format & 1) { // 1 is the value for BOLD format
              text = `<strong>${text}</strong>`;
            }

            // Apply italic formatting
            if (format & 2) { // 2 is the value for ITALIC format
              text = `<em>${text}</em>`;
            }

            html += text;
          }
        });

        html += '</p>';
      });

      html += '</div>';
      return html;
    });
  };

  // Handle editor changes and save editor state
  const handleEditorChange = (editorState) => {
    if (editorState) {
      // Serialize the editor state to JSON
      const editorStateJSON = JSON.stringify(editorState.toJSON());

      // Get the plain text content
      const plainText = editorState.read(() => {
        return $getRoot().getTextContent();
      });

      // Generate HTML from the editor state with proper formatting
      const htmlContent = generateHtmlFromEditorState(editorState);

      // Call the onChange callback with the editor state, plain text, and HTML
      onChange(editorStateJSON, plainText, htmlContent);
    }
  };

  // Create the editor style based on the textStyle prop
  const editorStyle = {
    fontFamily: textStyle.fontFamily,
    fontSize: textStyle.fontSize,
    fontWeight: textStyle.fontWeight,
    fontStyle: textStyle.fontStyle,
    textAlign: textStyle.textAlign,
    color: textStyle.color,
    backgroundColor: textStyle.backgroundColor,
    height: '100%',
    width: '100%',
    outline: 'none',
    padding: '4px',
    boxSizing: 'border-box'
  };

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <div className="editor-container" style={editorStyle}>
        {/* Include the toolbar inside the LexicalComposer */}
        <RichTextToolbar
          position={toolbarPosition}
          textStyle={textStyle}
          onFormatChange={() => {
            // We don't need this anymore since formatting is handled by Lexical
          }}
        />

        <RichTextPlugin
          contentEditable={
            <ContentEditable
              className="editor-input"
              onBlur={onBlur}
              onFocus={onFocus}
              onKeyDown={onKeyDown}
              ariaLabel="Rich Text Editor"
              spellCheck={true}
              autoFocus={true}
              data-testid="editor-input"
            />
          }
          placeholder={<div className="editor-placeholder">Enter text...</div>}
        />
        <HistoryPlugin />
        <OnChangePlugin onChange={handleEditorChange} />
        <EditorUpdater content={content} textStyle={textStyle} editorState={editorState} />
      </div>
    </LexicalComposer>
  );
};

RichTextEditor.propTypes = {
  content: PropTypes.string,
  textStyle: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onKeyDown: PropTypes.func,
  toolbarPosition: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired
  }).isRequired,
  editorState: PropTypes.string
};

RichTextEditor.defaultProps = {
  content: '',
  onBlur: () => {},
  onFocus: () => {},
  onKeyDown: () => {},
  editorState: null
};

export default RichTextEditor;
