.table-component-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.table-component-container table {
  width: 100%;
  height: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.table-component-container th,
.table-component-container td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.table-component-container th {
  font-weight: bold;
  text-align: left;
}

/* Ensure table fits within component bounds */
.table-component-container {
  box-sizing: border-box;
}

/* Handle zoom scaling */
.draggable-component .table-component-container {
  transform-origin: top left;
}

/* Selection styles */
.draggable-component.selected .table-component-container {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

/* Hover effects for better UX */
.table-component-container tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1) !important;
}

/* Responsive text sizing */
.table-component-container table {
  font-size: inherit;
  font-family: inherit;
  color: inherit;
}
