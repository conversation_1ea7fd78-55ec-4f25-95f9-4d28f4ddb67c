import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useReport } from '../../hooks/useReport';
import { logout } from '../../redux/actions/authActions';
import { generateReportUrl } from '../../utils/urlUtils';
import './ReportHeader.css';

/**
 * Header component for the report editor
 */
const ReportHeader = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const user = useSelector(state => state.auth.user);
  const {
    activeReport,
    createNewReport,
    updateReport,
    addPage,
    deletePage,
    saveReport
  } = useReport();

  // Handle user logout
  const handleLogout = async () => {
    try {
      await dispatch(logout());
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Handle saving the current report
  const handleSaveReport = async () => {
    if (activeReport) {
      try {
        console.log('Saving report:', activeReport.id);
        const result = await saveReport(activeReport.id, activeReport);
        console.log('Report saved successfully:', result);
        return result;
      } catch (error) {
        console.error('Save report failed:', error);
        throw error;
      }
    }
    return null;
  };

  // Handle sharing the report URL
  const handleShareReport = () => {
    if (activeReport && user) {
      // Make sure the report is saved first
      handleSaveReport().then(() => {
        const reportUrl = generateReportUrl(user.uid, activeReport.name, activeReport.id);
        const fullUrl = window.location.origin + reportUrl;

        console.log('Generated report URL:', reportUrl);
        console.log('Full URL:', fullUrl);

        // Only update URL if it's different from current URL to prevent unnecessary re-renders
        if (window.location.pathname !== reportUrl) {
          window.history.replaceState(null, '', reportUrl);
        }

        // Copy to clipboard
        navigator.clipboard.writeText(fullUrl)
          .then(() => {
            alert('Report URL copied to clipboard!');
          })
          .catch(err => {
            console.error('Failed to copy URL: ', err);
          });
      });
    }
  };

  const [isRenaming, setIsRenaming] = useState(false);
  const [reportName, setReportName] = useState('');

  // Handle report name change
  const handleNameChange = (e) => {
    setReportName(e.target.value);
  };

  // Start renaming the report
  const startRenaming = () => {
    if (activeReport) {
      setReportName(activeReport.name);
      setIsRenaming(true);
    }
  };

  // Save the new report name
  const saveReportName = () => {
    if (activeReport && reportName.trim()) {
      updateReport(activeReport.id, { name: reportName.trim() });
      setIsRenaming(false);
    }
  };

  // Handle key press in the rename input
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      saveReportName();
    } else if (e.key === 'Escape') {
      setIsRenaming(false);
    }
  };

  // Create a new report and set it as active
  const handleNewReport = () => {
    // Use the new parameter to automatically set the report as active
    createNewReport('Untitled Report', true);
  };

  // Add a new page to the active report after the current active page
  const handleAddPage = () => {
    if (activeReport) {
      // Insert the new page after the current active page
      addPage(activeReport.id, activeReport.activePageIndex);
    }
  };

  // Delete the active page
  const handleDeletePage = () => {
    if (activeReport && activeReport.pages.length > 1) {
      const activePage = activeReport.pages[activeReport.activePageIndex];
      deletePage(activeReport.id, activePage.id);
    }
  };

  return (
    <header className="report-header">
      {activeReport ? (
        <>
          <div className="report-title">
            {isRenaming ? (
              <input
                type="text"
                value={reportName}
                onChange={handleNameChange}
                onBlur={saveReportName}
                onKeyDown={handleKeyPress}
                className="report-title-input"
                autoFocus
              />
            ) : (
              <h1 onClick={startRenaming}>
                {activeReport.name}
                <span className="edit-icon" title="Rename report">✎</span>
              </h1>
            )}
          </div>

          <div className="report-actions">
            <button
              className="action-button"
              onClick={handleNewReport}
              title="Create new report"
            >
              New Report
            </button>

            <button
              className="action-button"
              onClick={handleAddPage}
              title="Add new page"
            >
              Add Page
            </button>

            <button
              className="action-button"
              onClick={handleDeletePage}
              disabled={activeReport.pages.length <= 1}
              title={activeReport.pages.length <= 1 ? "Cannot delete the only page" : "Delete current page"}
            >
              Delete Page
            </button>

            <button
              className="action-button"
              onClick={handleSaveReport}
              title="Save report to cloud"
            >
              Save
            </button>

            <button
              className="action-button"
              onClick={handleShareReport}
              title="Share report URL"
            >
              Share
            </button>

            <button
              className="action-button primary"
              title="Export report"
            >
              Export
            </button>

            <button
              className="action-button logout-button"
              onClick={handleLogout}
              title="Log out"
            >
              Log Out
            </button>
          </div>
        </>
      ) : (
        <>
          <div className="report-title">
            <h1>Exporter</h1>
          </div>

          <div className="report-actions">
            <button
              className="action-button primary"
              onClick={handleNewReport}
              title="Create new report"
            >
              New Report
            </button>

            <button
              className="action-button logout-button"
              onClick={handleLogout}
              title="Log out"
            >
              Log Out
            </button>
          </div>
        </>
      )}
    </header>
  );
};

export default ReportHeader;
