/* Report header styles */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 1rem;
  background-color: #333; /* Keep the original dark gray background */
  color: white;
  height: 50px;
}

.report-title h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.edit-icon {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.report-title h1:hover .edit-icon {
  opacity: 1;
}

.report-title-input {
  background-color: var(--color-secondary);
  border: none;
  border-radius: 3px;
  color: var(--color-text-light);
  font-size: 1.25rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  width: 300px;
  max-width: 100%;
}

.report-title-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-tertiary);
}

.report-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background-color: #555;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #666;
}

.action-button:active {
  background-color: #444;
}

.action-button.primary {
  background-color: #4a90e2;
}

.action-button.primary:hover {
  background-color: #3a80d2;
}

.action-button:disabled {
  background-color: #444;
  color: #888;
  cursor: not-allowed;
}

.action-button.logout-button {
  background-color: #d9534f;
}

.action-button.logout-button:hover {
  background-color: #c9302c;
}
