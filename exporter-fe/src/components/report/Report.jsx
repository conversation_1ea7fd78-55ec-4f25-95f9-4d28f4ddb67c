import { useState, useRef, useEffect } from 'react';
import Page from '../page/Page';
import { useReport } from '../../hooks/useReport';
import { calculateFitAllZoom as calcFitAll, calculateFitWidthZoom as calcFitWidth } from '../../utils/unitConversion';
import { getPageDimensions } from '../../models/ReportTypes';
import './Report.css';

/**
 * Main report component that displays pages and handles zoom
 */
const Report = () => {
  // Get report context
  const { activeReport, activePage, setActivePage } = useReport();

  // State for zoom
  const [zoomLevel, setZoomLevel] = useState(100);
  const [zoomMode, setZoomMode] = useState('fit-all'); // 'custom', 'fit-width', 'fit-all'

  // Refs
  const documentAreaRef = useRef(null);
  const isUpdatingRef = useRef(false); // Use this to prevent infinite loops
  const zoomInitializedRef = useRef(false); // Track if zoom has been initialized

  // Calculate initial zoom to fit the page - run only once when first page is available
  useEffect(() => {
    // Only run once when we first have an active page and zoom hasn't been initialized
    if (!activePage || zoomInitializedRef.current) return;

    // Use requestAnimationFrame to ensure DOM is ready
    const calculateInitialZoom = () => {
      if (documentAreaRef.current && activePage && !zoomInitializedRef.current) {
        // Prevent infinite loops
        if (isUpdatingRef.current) return;
        isUpdatingRef.current = true;

        try {
          const containerDimensions = {
            width: documentAreaRef.current.clientWidth,
            height: documentAreaRef.current.clientHeight
          };

          const pageDimensions = getPageDimensions(activePage);

          const newZoomLevel = calcFitAll(containerDimensions, pageDimensions);
          console.log('Initial zoom level (one-time):', newZoomLevel);
          setZoomLevel(newZoomLevel);
          setZoomMode('fit-all');

          // Mark zoom as initialized
          zoomInitializedRef.current = true;

          // Ensure scrollbar is at the top
          documentAreaRef.current.scrollTop = 0;
          documentAreaRef.current.scrollLeft = 0;
        } finally {
          isUpdatingRef.current = false;
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM measurements are accurate
    requestAnimationFrame(calculateInitialZoom);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activePage]); // Only depend on activePage, but use ref to prevent multiple initializations

  // Log zoom level changes (only when user explicitly changes zoom)
  useEffect(() => {
    // Only log if zoom has been initialized to avoid logging initial state
    if (zoomInitializedRef.current) {
      console.log('Zoom changed - Level:', zoomLevel, 'Mode:', zoomMode);
    }
  }, [zoomLevel, zoomMode]);

  // Note: Removed automatic zoom recalculation when page changes
  // Users now maintain their zoom level when switching between pages
  // Zoom only changes when explicitly using zoom controls

  // Note: Removed forceUpdateKey as it was causing page selection issues
  // Zoom changes are now handled directly by each page component

  // Handle window resize
  useEffect(() => {
    // Skip if there's no active page
    if (!activePage) return;

    const handleResize = () => {
      // Use requestAnimationFrame to ensure DOM measurements are accurate
      requestAnimationFrame(() => {
        // Prevent infinite loops by checking if we're already updating
        if (isUpdatingRef.current) return;

        if (!documentAreaRef.current) return;

        // Set the flag to indicate we're updating
        isUpdatingRef.current = true;

        try {
          const containerDimensions = {
            width: documentAreaRef.current.clientWidth,
            height: documentAreaRef.current.clientHeight
          };

          const pageDimensions = getPageDimensions(activePage);

          if (zoomMode === 'fit-width') {
            const newZoomLevel = calcFitWidth(containerDimensions, pageDimensions);
            setZoomLevel(newZoomLevel);
          } else if (zoomMode === 'fit-all') {
            const newZoomLevel = calcFitAll(containerDimensions, pageDimensions);
            setZoomLevel(newZoomLevel);

            // Reset scroll position when in fit-all mode
            documentAreaRef.current.scrollTop = 0;
            documentAreaRef.current.scrollLeft = 0;
          }
        } finally {
          // Reset the flag after the update is complete
          isUpdatingRef.current = false;
        }
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activePage, zoomMode]);

  // Calculate zoom level to fit the entire page
  const calculateFitAllZoom = () => {
    if (!documentAreaRef.current || !activePage) return;

    // Use requestAnimationFrame to ensure DOM measurements are accurate
    requestAnimationFrame(() => {
      // Prevent infinite loops
      if (isUpdatingRef.current) return;
      isUpdatingRef.current = true;

      try {
        const containerDimensions = {
          width: documentAreaRef.current.clientWidth,
          height: documentAreaRef.current.clientHeight
        };

        const pageDimensions = getPageDimensions(activePage);

        // Log dimensions for debugging
        console.log('Fit All - Container dimensions (px):', containerDimensions);
        console.log('Fit All - Page dimensions (mm):', pageDimensions);

        const newZoomLevel = calcFitAll(containerDimensions, pageDimensions);
        console.log('Fit All - Calculated zoom level:', newZoomLevel);

        setZoomLevel(newZoomLevel);
        setZoomMode('fit-all');

        // Reset scroll position when using fit-all
        documentAreaRef.current.scrollTop = 0;
        documentAreaRef.current.scrollLeft = 0;
      } finally {
        isUpdatingRef.current = false;
      }
    });
  };

  // Calculate zoom level to fit the width
  const calculateFitWidthZoom = () => {
    if (!documentAreaRef.current || !activePage) return;

    // Prevent infinite loops
    if (isUpdatingRef.current) return;
    isUpdatingRef.current = true;

    try {
      const containerDimensions = {
        width: documentAreaRef.current.clientWidth,
        height: documentAreaRef.current.clientHeight
      };

      const pageDimensions = getPageDimensions(activePage);

      // Log dimensions for debugging
      console.log('Fit Width - Container dimensions (px):', containerDimensions);
      console.log('Fit Width - Page dimensions (mm):', pageDimensions);

      const newZoomLevel = calcFitWidth(containerDimensions, pageDimensions);
      console.log('Fit Width - Calculated zoom level:', newZoomLevel);

      setZoomLevel(newZoomLevel);
      setZoomMode('fit-width');
    } finally {
      isUpdatingRef.current = false;
    }
  };

  // Zoom in by 10%
  const zoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 10, 200));
    setZoomMode('custom');
  };

  // Zoom out by 10%
  const zoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 10, 20));
    setZoomMode('custom');
  };

  // Set custom zoom level
  const handleZoomChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value >= 20 && value <= 200) {
      setZoomLevel(value);
      setZoomMode('custom');
    }
  };

  // Store scroll position for each page
  const pageScrollPositions = useRef({});

  // Store the current scroll position before changing pages
  const saveScrollPosition = () => {
    if (activeReport && documentAreaRef.current) {
      pageScrollPositions.current[activeReport.activePageIndex] = {
        top: documentAreaRef.current.scrollTop,
        left: documentAreaRef.current.scrollLeft
      };
    }
  };

  // This effect handles programmatic page changes that don't come from clicks
  // Page clicks are handled by handlePageClick function with different behavior for active vs inactive pages
  useEffect(() => {
    // Skip if there's no active report or document area
    if (!activeReport || !documentAreaRef.current) return;

    // Skip if the page change was triggered by a click (which has its own handling)
    // We can detect this by checking if we're in the middle of a click handler
    if (isUpdatingRef.current) return;

    const pageIndex = activeReport.activePageIndex;
    const savedPosition = pageScrollPositions.current[pageIndex];

    if (savedPosition) {
      // Use requestAnimationFrame to ensure the DOM has updated
      requestAnimationFrame(() => {
        if (documentAreaRef.current) {
          documentAreaRef.current.scrollTop = savedPosition.top;
          documentAreaRef.current.scrollLeft = savedPosition.left;
        }
      });
    } else {
      // If no saved position, scroll to the active page
      setTimeout(() => {
        const activePageElement = document.getElementById(`page-${activeReport.pages[pageIndex].id}`);
        if (activePageElement && documentAreaRef.current) {
          // Calculate the offset of all previous pages
          let offsetTop = 0;
          for (let i = 0; i < pageIndex; i++) {
            const prevPageElement = document.getElementById(`page-${activeReport.pages[i].id}`);
            if (prevPageElement) {
              offsetTop += prevPageElement.offsetHeight + 24; // 1.5rem is the gap between pages (matches CSS)
            }
          }

          // Scroll the page into view
          documentAreaRef.current.scrollTop = offsetTop;
          documentAreaRef.current.scrollLeft = 0;

          console.log('Scrolling to page (from useEffect)', pageIndex, 'at position', offsetTop);
        }
      }, 50); // Small delay to ensure DOM is updated
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeReport?.activePageIndex]);

  // Handle page click
  const handlePageClick = (pageIndex) => {
    if (activeReport) {
      console.log('Page clicked:', pageIndex, 'Current active:', activeReport.activePageIndex);

      // Always set the active page (this handles both selection and deselection of components)
      setActivePage(activeReport.id, pageIndex);

      // If it's a different page, scroll to it
      if (pageIndex !== activeReport.activePageIndex) {
        // Save current scroll position before changing pages
        saveScrollPosition();

        // Scroll to the new page without changing zoom
        setTimeout(() => {
          const activePageElement = document.getElementById(`page-${activeReport.pages[pageIndex].id}`);
          if (activePageElement && documentAreaRef.current) {
            // Calculate the offset of all previous pages
            let offsetTop = 0;
            for (let i = 0; i < pageIndex; i++) {
              const prevPageElement = document.getElementById(`page-${activeReport.pages[i].id}`);
              if (prevPageElement) {
                offsetTop += prevPageElement.offsetHeight + 24; // 1.5rem is the gap between pages (matches CSS)
              }
            }

            // Scroll the page into view while maintaining current zoom
            documentAreaRef.current.scrollTop = offsetTop;
            documentAreaRef.current.scrollLeft = 0;

            console.log('Scrolling to page', pageIndex, 'at position', offsetTop);
          }
        }, 50); // Small delay to ensure DOM is updated
      }
    }
  };

  // If no active report, show a message
  if (!activeReport) {
    return (
      <div className="no-report-message">
        <p>No report selected. Create a new report to get started.</p>
      </div>
    );
  }

  // Prepare the style for the pages container - no transform scale
  const pagesContainerStyle = {
    // No transform scale, we'll apply zoom directly to each page
    // Add a key based on zoom level to force re-render when zoom changes
    key: `zoom-${zoomLevel}`,
    padding: 0,
    margin: 0
  };

  return (
    <div className="report-container">
      <div
        className="document-area"
        ref={documentAreaRef}
        // Reset scroll position on render
        onLoad={() => {
          if (documentAreaRef.current) {
            documentAreaRef.current.scrollTop = 0;
            documentAreaRef.current.scrollLeft = 0;
          }
        }}
      >
        <div
          className="pages-container"
          style={pagesContainerStyle}
        >
          {activeReport.pages.map((page, index) => (
            <Page
              key={page.id} // Use stable page ID as key to prevent unnecessary re-renders
              page={page}
              zoom={zoomLevel} // Pass actual zoom level to apply directly to the page
              isActive={index === activeReport.activePageIndex}
              onClick={() => handlePageClick(index)}
            />
          ))}
        </div>
      </div>

      <div className="report-footer">
        <div className="page-indicator">
          Page {activeReport.activePageIndex + 1} of {activeReport.pages.length}
        </div>

        <div className="zoom-controls">
          <button className="zoom-button" onClick={zoomOut} title="Zoom Out">-</button>

          <div className="zoom-input-container">
            <input
              type="number"
              min="20"
              max="200"
              value={zoomLevel}
              onChange={handleZoomChange}
              className="zoom-input"
            />
            <span className="zoom-percent">%</span>
          </div>

          <button className="zoom-button" onClick={zoomIn} title="Zoom In">+</button>

          <div className="zoom-presets">
            <button
              className={`zoom-preset-button ${zoomMode === 'fit-width' ? 'active' : ''}`}
              onClick={calculateFitWidthZoom}
              title="Fit to Width"
            >
              Width
            </button>
            <button
              className={`zoom-preset-button ${zoomMode === 'fit-all' ? 'active' : ''}`}
              onClick={calculateFitAllZoom}
              title="Fit All"
            >
              All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Report;
