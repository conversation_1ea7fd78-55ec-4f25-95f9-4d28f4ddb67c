/* Report component styles */
.report-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.document-area {
  flex: 1;
  background-color: #666; /* Keep the original gray background */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Align content to the top */
  padding: 20px 40px; /* 20px top/bottom padding, 40px left/right padding */
  box-sizing: border-box;
  cursor: default; /* Default cursor for the document area */

  /* Enable scrolling only when needed */
  overflow: auto;
  min-width: 0; /* Allow container to shrink */

  /* Hide scrollbars when not needed */
  scrollbar-gutter: stable;

  /* Ensure content doesn't cause unnecessary scrollbars */
  min-height: 0;
  height: calc(100vh - 80px); /* Fixed height to fit snugly with header and footer */

  /* For Firefox */
  scrollbar-width: thin;
  scrollbar-color: var(--color-secondary) var(--color-primary);

  /* Prevent any extra space */
  margin: 0;
}

/* Pages container for better horizontal scrolling */
.pages-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: min-content; /* Ensure container takes at least the width of its content */
  width: 100%;
  /* Fixed spacing between pages */
  gap: 1.5rem; /* Slightly reduced from 2rem for a more compact layout */
  margin: 0 auto; /* Center the container horizontally with no vertical margin */
  padding: 0; /* Remove any padding */
}

/* Custom scrollbar styling for Webkit browsers */
.document-area::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.document-area::-webkit-scrollbar-track {
  background: var(--color-primary);
}

.document-area::-webkit-scrollbar-thumb {
  background-color: var(--color-secondary);
  border-radius: 4px;
}

.report-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 1rem;
  background-color: #333; /* Keep the original dark gray background */
  color: white;
  height: 35px;
  font-size: 0.75rem;
  margin: 0; /* Ensure no margin is added */
  box-sizing: border-box; /* Ensure padding and border are included in the element's height */
}

.page-indicator {
  font-size: 0.75rem;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.zoom-button {
  width: 24px;
  height: 24px;
  background-color: #555;
  color: white;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: background-color 0.2s;
  cursor: pointer;
}

.zoom-button:hover {
  background-color: #666;
}

.zoom-button:active {
  background-color: #444;
  transform: scale(0.95);
}

.zoom-input-container {
  display: flex;
  align-items: center;
  background-color: #444;
  border-radius: 3px;
  padding: 0 0.25rem;
  height: 24px;
}

.zoom-input {
  width: 40px;
  background-color: transparent;
  border: none;
  color: white;
  font-size: 0.75rem;
  text-align: right;
  padding: 0;
}

.zoom-input:focus {
  outline: none;
}

.zoom-input::-webkit-inner-spin-button,
.zoom-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.zoom-percent {
  color: white;
  font-size: 0.75rem;
  margin-left: 0.125rem;
}

.zoom-presets {
  display: flex;
  gap: 0.25rem;
}

.zoom-preset-button {
  background-color: #555;
  color: white;
  border-radius: 3px;
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  transition: all 0.2s;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-preset-button:hover {
  background-color: #666;
}

.zoom-preset-button.active {
  background-color: #777;
  font-weight: bold;
}

.zoom-preset-button:active {
  background-color: #444;
  transform: scale(0.95);
}

.no-report-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
  font-size: 1.2rem;
  text-align: center;
  padding: 2rem;
}

/* Error and loading states */
.app-loading,
.error-message,
.not-found-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  text-align: center;
  padding: 2rem;
  box-sizing: border-box;
}

.app-loading {
  background-color: rgba(0, 0, 0, 0.03);
}

.error-message {
  background-color: rgba(255, 0, 0, 0.05);
}

.not-found-message {
  background-color: rgba(0, 0, 0, 0.03);
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-details,
.error-details {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.cancel-button,
.home-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.cancel-button:hover,
.home-button:hover {
  background-color: #e0e0e0;
}

.error-message h2,
.not-found-message h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
}
