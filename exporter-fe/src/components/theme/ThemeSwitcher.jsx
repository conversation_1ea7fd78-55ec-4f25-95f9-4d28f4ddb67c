import React from 'react';
import { useTheme } from '../../theme/ThemeContext';
import { THEMES } from '../../theme/ThemeConfig';
import './ThemeSwitcher.css';

/**
 * Theme switcher component
 * Allows users to switch between different themes
 */
const ThemeSwitcher = () => {
  const { currentTheme, changeTheme } = useTheme();

  return (
    <div className="theme-switcher">
      <select 
        value={currentTheme.id}
        onChange={(e) => changeTheme(e.target.value)}
        className="theme-select"
      >
        {THEMES.map(theme => (
          <option key={theme.id} value={theme.id}>
            {theme.name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default ThemeSwitcher;
