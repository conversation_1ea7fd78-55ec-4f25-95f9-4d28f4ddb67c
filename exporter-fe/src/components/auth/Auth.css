.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-background);
}

.auth-form-container {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-form-container h2 {
  margin-bottom: 1.5rem;
  text-align: center;
  color: var(--color-primary);
}

.auth-description {
  margin-bottom: 1.5rem;
  text-align: center;
  color: var(--color-text);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--color-text);
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-tertiary);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.auth-button {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: var(--color-tertiary);
  color: var(--color-text-light);
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auth-button:hover {
  background-color: var(--color-primary);
}

.auth-button:disabled {
  background-color: var(--color-border);
  cursor: not-allowed;
}

.auth-links {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
}

.auth-link {
  background: none;
  border: none;
  color: var(--color-tertiary);
  font-size: 0.9rem;
  cursor: pointer;
  text-decoration: underline;
}

.auth-link:hover {
  color: var(--color-primary);
}

.auth-error {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--color-error);
  border-radius: 4px;
  color: var(--color-error);
  font-size: 0.9rem;
}

.auth-success {
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid var(--color-success);
  border-radius: 4px;
  color: var(--color-success);
  text-align: center;
}

.input-error {
  color: var(--color-error);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  color: var(--color-text);
  font-size: 0.9rem;
}

.auth-divider::before,
.auth-divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid var(--color-border);
}

.auth-divider span {
  padding: 0 0.75rem;
}

.google-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background-color: #fff;
  color: #757575;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
}

.google-button:hover {
  background-color: #f8f8f8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.google-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.google-icon {
  width: 18px;
  height: 18px;
}
