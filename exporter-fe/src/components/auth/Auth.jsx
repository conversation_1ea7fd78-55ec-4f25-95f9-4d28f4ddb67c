import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import Login from './Login';
import Register from './Register';
import ForgotPassword from './ForgotPassword';
import './Auth.css';

const Auth = () => {
  const [activeForm, setActiveForm] = useState('login');
  const { isAuthenticated } = useSelector(state => state.auth);
  
  // If user is already authenticated, don't show auth forms
  if (isAuthenticated) {
    return null;
  }
  
  const handleToggleForm = (formName) => {
    setActiveForm(formName);
  };
  
  return (
    <div className="auth-container">
      {activeForm === 'login' && (
        <Login onToggleForm={handleToggleForm} />
      )}
      
      {activeForm === 'register' && (
        <Register onToggleForm={handleToggleForm} />
      )}
      
      {activeForm === 'forgot-password' && (
        <ForgotPassword onToggleForm={handleToggleForm} />
      )}
    </div>
  );
};

export default Auth;
