import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { forgotPassword } from '../../redux/actions/authActions';
import './Auth.css';

const ForgotPassword = ({ onToggleForm }) => {
  const [email, setEmail] = useState('');
  const [success, setSuccess] = useState(false);
  
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector(state => state.auth);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      await dispatch(forgotPassword(email));
      setSuccess(true);
    } catch (error) {
      // Error is handled in the reducer
      console.error('Password reset failed:', error);
    }
  };

  return (
    <div className="auth-form-container">
      <h2>Reset Password</h2>
      
      {error && (
        <div className="auth-error">
          {error}
        </div>
      )}
      
      {success ? (
        <div className="auth-success">
          <p>Password reset email sent to {email}.</p>
          <p>Please check your email for further instructions.</p>
          <button
            type="button"
            className="auth-button"
            onClick={() => onToggleForm('login')}
          >
            Back to Login
          </button>
        </div>
      ) : (
        <>
          <p className="auth-description">
            Enter your email address and we'll send you a link to reset your password.
          </p>
          
          <form onSubmit={handleSubmit} className="auth-form">
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            <button
              type="submit"
              className="auth-button"
              disabled={isLoading}
            >
              {isLoading ? 'Sending...' : 'Send Reset Link'}
            </button>
          </form>
          
          <div className="auth-links">
            <button
              type="button"
              className="auth-link"
              onClick={() => onToggleForm('login')}
            >
              Back to Login
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ForgotPassword;
