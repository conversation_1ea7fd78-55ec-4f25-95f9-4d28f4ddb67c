import { useSelector, useDispatch } from 'react-redux';
import {
  createNewReport,
  updateReport,
  deleteReport,
  setActiveReport,
  addPage,
  updatePage,
  deletePage,
  setActivePage,
  addComponent,
  updateComponent,
  deleteComponent,
  selectComponent,
  loadReports,
  saveReport,
  loadReportById
} from '../redux/actions/reportActions';

/**
 * Custom hook to access report state and actions
 * This provides a similar interface to the original useReport hook
 * @returns {Object} Report state and actions
 */
export const useReport = () => {
  const dispatch = useDispatch();

  // Get report state from Redux
  const reports = useSelector(state => state.report.reports);
  const activeReportId = useSelector(state => state.report.activeReportId);
  const isLoading = useSelector(state => state.report.isLoading);
  const error = useSelector(state => state.report.error);

  // Get the active report
  const activeReport = reports.find(report => report.id === activeReportId) || null;

  // Get the active page
  const activePage = activeReport ? activeReport.pages[activeReport.activePageIndex] || null : null;

  // Return the same interface as the original useReport hook
  return {
    // State
    reports,
    activeReport,
    activePage,
    isLoading,
    error,

    // Actions
    createNewReport: (name, setActive) => dispatch(createNewReport(name, setActive)),
    updateReport: (reportId, updates) => dispatch(updateReport(reportId, updates)),
    deleteReport: (reportId) => dispatch(deleteReport(reportId)),
    setActiveReport: (reportId) => dispatch(setActiveReport(reportId)),
    addPage: (reportId, afterPageIndex) => dispatch(addPage(reportId, afterPageIndex)),
    updatePage: (reportId, pageId, updates) => dispatch(updatePage(reportId, pageId, updates)),
    deletePage: (reportId, pageId) => dispatch(deletePage(reportId, pageId)),
    setActivePage: (reportId, pageIndex) => dispatch(setActivePage(reportId, pageIndex)),
    addComponent: (reportId, pageId, component) => dispatch(addComponent(reportId, pageId, component)),
    updateComponent: (reportId, pageId, componentId, updates) =>
      dispatch(updateComponent(reportId, pageId, componentId, updates)),
    deleteComponent: (reportId, pageId, componentId) =>
      dispatch(deleteComponent(reportId, pageId, componentId)),
    selectComponent: (reportId, pageId, componentId) =>
      dispatch(selectComponent(reportId, pageId, componentId)),
    loadReports: () => dispatch(loadReports()),
    saveReport: (reportId, reportData) => dispatch(saveReport(reportId, reportData)),
    loadReportById: (reportId) => dispatch(loadReportById(reportId))
  };
};
