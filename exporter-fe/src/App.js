import React from 'react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from './theme/ThemeContext';
import AppRoutes from './routes/AppRoutes';
import store from './redux/store';
import './App.css';

/**
 * Main App component
 */
function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider>
          <AppRoutes />
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
}

export default App;
