import React, { createContext, useState, useContext, useEffect } from 'react';
import { DEFAULT_THEME, getThemeById, generateThemeVariables } from './ThemeConfig';

// Create a context for theme management
const ThemeContext = createContext();

/**
 * ThemeProvider component to manage theme state
 */
export const ThemeProvider = ({ children }) => {
  // Get the saved theme from localStorage or use default
  const [currentTheme, setCurrentTheme] = useState(() => {
    const savedThemeId = localStorage.getItem('theme');
    return savedThemeId ? getThemeById(savedThemeId) : DEFAULT_THEME;
  });

  // Apply theme when it changes
  useEffect(() => {
    // Save theme to localStorage
    localStorage.setItem('theme', currentTheme.id);
    
    // Apply CSS variables to the document root
    document.documentElement.style.cssText = generateThemeVariables(currentTheme);
    
    // Add a data attribute for theme-specific selectors
    document.documentElement.setAttribute('data-theme', currentTheme.id);
  }, [currentTheme]);

  // Change the current theme
  const changeTheme = (themeId) => {
    const newTheme = getThemeById(themeId);
    setCurrentTheme(newTheme);
  };

  // Context value
  const value = {
    currentTheme,
    changeTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use the theme context
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
