import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
  sendPasswordResetEmail,
  onAuthStateChanged,
  updateProfile
} from 'firebase/auth';
import { auth, googleProvider } from '../config/firebase';

/**
 * Register a new user
 * @param {string} email - User email
 * @param {string} password - User password
 * @param {string} displayName - User display name
 * @returns {Promise<Object>} - User data
 */
export const register = async (email, password, displayName) => {
  try {
    // Create user in Firebase Authentication
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);

    // Update user profile with display name
    if (displayName) {
      await updateProfile(userCredential.user, { displayName });
    }

    // Get ID token for backend authentication
    const idToken = await userCredential.user.getIdToken();

    // Register user in backend
    const response = await fetch(`${process.env.REACT_APP_API_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${idToken}`
      },
      body: JSON.stringify({
        email,
        displayName: displayName || email.split('@')[0]
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to register user');
    }

    return userCredential.user;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

/**
 * Login a user with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - User data
 */
export const login = async (email, password) => {
  try {
    // Sign in with Firebase Authentication
    const userCredential = await signInWithEmailAndPassword(auth, email, password);

    // Get ID token for backend authentication
    const idToken = await userCredential.user.getIdToken();

    // Login user in backend
    const response = await fetch(`${process.env.REACT_APP_API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ idToken })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to login');
    }

    return userCredential.user;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

/**
 * Login a user with Google
 * @returns {Promise<Object>} - User data
 */
export const loginWithGoogle = async () => {
  try {
    // Sign in with Google using Firebase Authentication
    const userCredential = await signInWithPopup(auth, googleProvider);

    // Get ID token for backend authentication
    const idToken = await userCredential.user.getIdToken();

    // Login user in backend
    const response = await fetch(`${process.env.REACT_APP_API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ idToken })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to login with Google');
    }

    return userCredential.user;
  } catch (error) {
    console.error('Google login error:', error);
    throw error;
  }
};

/**
 * Logout a user
 * @returns {Promise<void>}
 */
export const logout = async () => {
  try {
    // Get current user's ID token before signing out
    const idToken = await auth.currentUser?.getIdToken();

    // Sign out from Firebase Authentication
    await signOut(auth);

    // If we have an ID token, notify the backend
    if (idToken) {
      await fetch(`${process.env.REACT_APP_API_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        }
      });
    }
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};

/**
 * Send password reset email
 * @param {string} email - User email
 * @returns {Promise<void>}
 */
export const forgotPassword = async (email) => {
  try {
    // Send password reset email with Firebase Authentication
    await sendPasswordResetEmail(auth, email);

    // Notify backend
    await fetch(`${process.env.REACT_APP_API_URL}/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    throw error;
  }
};

/**
 * Get current user
 * @returns {Promise<Object|null>} - User data or null if not authenticated
 */
export const getCurrentUser = () => {
  return new Promise((resolve, reject) => {
    const unsubscribe = onAuthStateChanged(
      auth,
      (user) => {
        unsubscribe();
        resolve(user);
      },
      (error) => {
        reject(error);
      }
    );
  });
};

/**
 * Get ID token for authenticated user
 * @returns {Promise<string|null>} - ID token or null if not authenticated
 */
export const getIdToken = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return null;
    }

    return await user.getIdToken();
  } catch (error) {
    console.error('Get ID token error:', error);
    return null;
  }
};
