import { getIdToken } from './authService';
import { collection, addDoc, getDocs, getDoc, updateDoc, deleteDoc, doc, query, where, orderBy, setDoc } from 'firebase/firestore';
import { firestore } from '../config/firebase';
import { auth } from '../config/firebase';
import { generateReportId } from '../utils/urlUtils';

/**
 * Get all reports for the current user
 * @returns {Promise<Array>} - Array of reports
 */
export const getAllReports = async () => {
  try {
    // Check if user is authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get reports directly from Firestore
    const reportsRef = collection(firestore, 'reports');
    const q = query(
      reportsRef,
      where('userId', '==', currentUser.uid),
      orderBy('updatedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const reports = [];

    querySnapshot.forEach((doc) => {
      reports.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return reports;
  } catch (error) {
    console.error('Get reports error:', error);
    throw error;
  }
};

/**
 * Get a report by ID
 * @param {string} id - Report ID
 * @returns {Promise<Object>} - Report data
 */
export const getReportById = async (id) => {
  try {
    console.log('getReportById called with ID:', id);

    // Check if user is authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('User not authenticated');
      throw new Error('User not authenticated');
    }

    console.log('Current user:', currentUser.uid);

    // We'll try multiple ID formats to find the report
    const idsToTry = [];

    // First, try the ID as-is
    idsToTry.push(id);

    // Then try with 'report-' prefix if it doesn't have it
    if (!id.startsWith('report-')) {
      idsToTry.push(`report-${id}`);
    }

    // Also try without the prefix if it has it
    if (id.startsWith('report-')) {
      idsToTry.push(id.substring(7));
    }

    console.log('Will try these IDs in order:', idsToTry);

    // Try each ID format until we find the report
    let reportDoc = null;
    let foundId = null;

    for (const idToTry of idsToTry) {
      console.log(`Trying ID: ${idToTry}`);
      const reportRef = doc(firestore, 'reports', idToTry);
      const docSnapshot = await getDoc(reportRef);

      if (docSnapshot.exists()) {
        console.log(`Found report with ID: ${idToTry}`);
        reportDoc = docSnapshot;
        foundId = idToTry;
        break;
      } else {
        console.log(`No report found with ID: ${idToTry}`);
      }
    }

    if (!reportDoc) {
      console.error('Report not found in Firestore after trying all ID formats');
      throw new Error('Report not found');
    }

    const reportData = reportDoc.data();

    // Check if the report belongs to the current user
    if (reportData.userId !== currentUser.uid) {
      console.error('Permission denied. Report belongs to:', reportData.userId);
      throw new Error('You do not have permission to access this report');
    }

    console.log('Report data retrieved successfully');
    return {
      id: foundId, // Use the ID that worked
      ...reportData
    };
  } catch (error) {
    console.error('Get report error:', error);
    throw error;
  }
};

/**
 * Create a new report
 * @param {Object} reportData - Report data
 * @returns {Promise<Object>} - Created report
 */
export const createReport = async (reportData) => {
  try {
    // Check if user is authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Add user ID to report data
    const reportWithUser = {
      ...reportData,
      userId: currentUser.uid
    };

    // Generate a custom ID for the report if not provided
    const reportId = reportData.id || `report-${generateReportId(reportData.name)}`;

    // Create report directly in Firestore with the custom ID
    const reportRef = doc(firestore, 'reports', reportId);
    await setDoc(reportRef, reportWithUser);

    return {
      id: reportId,
      ...reportWithUser
    };
  } catch (error) {
    console.error('Create report error:', error);
    throw error;
  }
};

/**
 * Update a report
 * @param {string} id - Report ID
 * @param {Object} reportData - Report data
 * @returns {Promise<Object>} - Updated report
 */
export const updateReport = async (id, reportData) => {
  try {
    // Check if user is authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the existing report to check permissions
    const reportRef = doc(firestore, 'reports', id);
    const reportDoc = await getDoc(reportRef);

    if (!reportDoc.exists()) {
      throw new Error('Report not found');
    }

    const existingReport = reportDoc.data();

    // Check if the report belongs to the current user
    if (existingReport.userId !== currentUser.uid) {
      throw new Error('You do not have permission to update this report');
    }

    // Add updated timestamp
    const updatedData = {
      ...reportData,
      updatedAt: new Date().toISOString()
    };

    // Update report directly in Firestore
    await updateDoc(reportRef, updatedData);

    return {
      id,
      ...existingReport,
      ...updatedData
    };
  } catch (error) {
    console.error('Update report error:', error);
    throw error;
  }
};

/**
 * Delete a report
 * @param {string} id - Report ID
 * @returns {Promise<void>}
 */
export const deleteReport = async (id) => {
  try {
    // Check if user is authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the existing report to check permissions
    const reportRef = doc(firestore, 'reports', id);
    const reportDoc = await getDoc(reportRef);

    if (!reportDoc.exists()) {
      throw new Error('Report not found');
    }

    const existingReport = reportDoc.data();

    // Check if the report belongs to the current user
    if (existingReport.userId !== currentUser.uid) {
      throw new Error('You do not have permission to delete this report');
    }

    // Delete report directly from Firestore
    await deleteDoc(reportRef);
  } catch (error) {
    console.error('Delete report error:', error);
    throw error;
  }
};

/**
 * Get a report by slug and ID
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} - Report data
 */
export const getReportBySlugAndId = async (reportId) => {
  try {
    // Check if user is authenticated
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get report directly from Firestore using the ID
    return await getReportById(reportId);
  } catch (error) {
    console.error('Get report by slug and ID error:', error);
    throw error;
  }
};
