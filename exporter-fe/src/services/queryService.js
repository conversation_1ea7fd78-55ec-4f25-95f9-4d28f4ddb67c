import { auth } from '../config/firebase';
import { createQuery as createQueryObject, validateQuery } from 'exporter-shared-utils';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

/**
 * Get authentication headers for API requests
 * @returns {Object} Headers object with authorization token
 */
const getAuthHeaders = async () => {
  const currentUser = auth.currentUser;
  if (!currentUser) {
    throw new Error('User not authenticated');
  }

  const token = await currentUser.getIdToken();
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
};

/**
 * Create a new query using shared utilities
 * @param {string} title - Query title
 * @param {string} sqlQuery - SQL query string
 * @param {string} description - Query description (optional)
 * @returns {Promise<Object>} - Created query
 */
export const createQuery = async (title, sqlQuery, description = '') => {
  try {
    // Use shared utility to create and validate query object
    const queryObject = createQueryObject(null, title, sqlQuery, description);

    // Validate using shared utility
    const validation = validateQuery(queryObject);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    const headers = await getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/query/createQuery`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        title: queryObject.title,
        query: queryObject.query,
        description: queryObject.description
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to create query');
    }

    return result.data;
  } catch (error) {
    console.error('Create query error:', error);
    throw error;
  }
};

/**
 * Get all queries for the current user
 * @returns {Promise<Array>} - Array of queries
 */
export const getAllQueries = async () => {
  try {
    const headers = await getAuthHeaders();
    
    const response = await fetch(`${API_BASE_URL}/queries`, {
      method: 'GET',
      headers
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to get queries');
    }

    return result.data;
  } catch (error) {
    console.error('Get queries error:', error);
    throw error;
  }
};

/**
 * Get a specific query by ID
 * @param {string} queryId - Query ID
 * @returns {Promise<Object>} - Query object
 */
export const getQueryById = async (queryId) => {
  try {
    const headers = await getAuthHeaders();
    
    const response = await fetch(`${API_BASE_URL}/queries/${queryId}`, {
      method: 'GET',
      headers
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to get query');
    }

    return result.data;
  } catch (error) {
    console.error('Get query error:', error);
    throw error;
  }
};
