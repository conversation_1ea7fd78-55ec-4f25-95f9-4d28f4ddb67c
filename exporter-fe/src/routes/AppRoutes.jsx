import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import Editor from '../components/editor/Editor';
import Auth from '../components/auth/Auth';
import ReportView from '../components/report/ReportView';
import QueryForm from '../components/query/QueryForm';
import { checkAuth } from '../redux/actions/authActions';

/**
 * Application routes component
 */
const AppRoutes = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, isLoading, user } = useSelector(state => state.auth);

  useEffect(() => {
    // Check if user is authenticated on app load
    dispatch(checkAuth());
  }, [dispatch]);

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className="app-loading">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  // If not authenticated, show auth screen
  if (!isAuthenticated) {
    return <Auth />;
  }

  return (
    <Routes>
      {/* Home route */}
      <Route path="/" element={<Editor />} />

      {/* Query testing route */}
      <Route path="/queries" element={<QueryForm />} />

      {/* User report routes */}
      <Route path="/user/:userId/report/:reportSlugAndId" element={<ReportView />} />

      {/* Redirect any other routes to home */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRoutes;
