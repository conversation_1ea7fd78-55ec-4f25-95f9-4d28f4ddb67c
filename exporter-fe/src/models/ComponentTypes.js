/**
 * Component type definitions for page elements
 */

// Component types
export const COMPONENT_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  TABLE: 'table'
};

/**
 * Creates a base component with common properties
 * @param {string} id - Unique identifier for the component
 * @param {number} x - X position in mm from the top left of the page
 * @param {number} y - Y position in mm from the top left of the page
 * @param {number} width - Width in mm
 * @param {number} height - Height in mm
 * @param {string} type - Component type
 * @returns {Object} A new component object
 */
export const createBaseComponent = (id, x, y, width, height, type) => ({
  id,
  x,
  y,
  width,
  height,
  type,
  rotation: 0, // in degrees
  locked: false,
  visible: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

/**
 * Creates a text component
 * @param {string} id - Unique identifier for the component
 * @param {number} x - X position in mm from the top left of the page
 * @param {number} y - Y position in mm from the top left of the page
 * @param {number} width - Width in mm
 * @param {number} height - Height in mm
 * @returns {Object} A new text component
 */
export const createTextComponent = (id, x = 0, y = 0, width = 100, height = 20) => ({
  ...createBaseComponent(id, x, y, width, height, COMPONENT_TYPES.TEXT),
  text: 'Text Component',
  editorState: null, // Serialized Lexical editor state for rich text formatting
  htmlContent: null, // HTML content for displaying formatted text
  fontFamily: 'Arial',
  fontSize: 12, // in pt
  fontWeight: 'normal',
  fontStyle: 'normal',
  textAlign: 'left',
  color: '#000000',
  backgroundColor: 'transparent'
});

/**
 * Creates an image component
 * @param {string} id - Unique identifier for the component
 * @param {number} x - X position in mm from the top left of the page
 * @param {number} y - Y position in mm from the top left of the page
 * @param {number} width - Width in mm
 * @param {number} height - Height in mm
 * @returns {Object} A new image component
 */
export const createImageComponent = (id, x = 0, y = 0, width = 100, height = 100) => ({
  ...createBaseComponent(id, x, y, width, height, COMPONENT_TYPES.IMAGE),
  src: '', // URL or data URI
  alt: 'Image',
  objectFit: 'contain', // 'contain', 'cover', 'fill'
  border: {
    width: 0,
    style: 'solid',
    color: '#000000'
  }
});

/**
 * Creates a table component
 * @param {string} id - Unique identifier for the component
 * @param {number} x - X position in mm from the top left of the page
 * @param {number} y - Y position in mm from the top left of the page
 * @param {number} width - Width in mm
 * @param {number} height - Height in mm
 * @returns {Object} A new table component
 */
export const createTableComponent = (id, x = 0, y = 0, width = 200, height = 120) => ({
  ...createBaseComponent(id, x, y, width, height, COMPONENT_TYPES.TABLE),
  data: [
    { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Editor' }
  ],
  columns: [
    { id: 'name', header: 'Name', accessorKey: 'name' },
    { id: 'email', header: 'Email', accessorKey: 'email' },
    { id: 'role', header: 'Role', accessorKey: 'role' }
  ],
  showHeader: true,
  showBorder: true,
  alternateRowColors: true,
  fontSize: 10, // in pt
  fontFamily: 'Arial',
  headerBackgroundColor: '#f5f5f5',
  borderColor: '#cccccc',
  textColor: '#000000'
});

/**
 * Checks if a component is within page boundaries
 * @param {Object} component - The component to check
 * @param {Object} pageDimensions - The page dimensions in mm
 * @param {Object} pageMargins - The page margins in mm
 * @returns {boolean} True if the component is within boundaries
 */
export const isComponentWithinBoundaries = (component, pageDimensions, pageMargins) => {
  const minX = pageMargins.left;
  const minY = pageMargins.top;
  const maxX = pageDimensions.width - pageMargins.right;
  const maxY = pageDimensions.height - pageMargins.bottom;

  // Check if the component is completely within the page boundaries
  return (
    component.x >= minX &&
    component.y >= minY &&
    (component.x + component.width) <= maxX &&
    (component.y + component.height) <= maxY
  );
};

/**
 * Constrains a component to stay within page boundaries
 * @param {Object} component - The component to constrain
 * @param {Object} pageDimensions - The page dimensions in mm
 * @param {Object} pageMargins - The page margins in mm
 * @returns {Object} The constrained component
 */
export const constrainComponentToBoundaries = (component, pageDimensions, pageMargins) => {
  const minX = pageMargins.left;
  const minY = pageMargins.top;
  const maxX = pageDimensions.width - pageMargins.right;
  const maxY = pageDimensions.height - pageMargins.bottom;

  // Create a new component with constrained position
  return {
    ...component,
    x: Math.max(minX, Math.min(component.x, maxX - component.width)),
    y: Math.max(minY, Math.min(component.y, maxY - component.height))
  };
};
