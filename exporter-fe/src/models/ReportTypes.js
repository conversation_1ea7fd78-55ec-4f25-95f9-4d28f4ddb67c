/**
 * Report and Page type definitions
 */

// Standard page sizes in mm
export const PAGE_SIZES = {
  'a4': { width: 210, height: 297, name: 'A4' },
  'letter': { width: 215.9, height: 279.4, name: 'US Letter' },
  'legal': { width: 215.9, height: 355.6, name: 'US Legal' },
  'a3': { width: 297, height: 420, name: 'A3' },
  'a5': { width: 148, height: 210, name: 'A5' },
  'custom': { width: 210, height: 297, name: 'Custom' } // Default to A4 dimensions
};

// Page orientation types
export const ORIENTATIONS = {
  PORTRAIT: 'portrait',
  LANDSCAPE: 'landscape'
};

/**
 * Creates a new page object with default properties
 * @param {string} id - Unique identifier for the page
 * @returns {Object} A new page object
 */
export const createPage = (id) => ({
  id,
  pageSize: 'a4',
  orientation: ORIENTATIONS.PORTRAIT,
  customSize: { width: 210, height: 297 }, // in mm
  components: [], // Array to store components (text, images, etc.)
  backgroundColor: '#ffffff',
  margins: { top: 20, right: 20, bottom: 20, left: 20 }, // in mm
  selectedComponentId: null, // Track the currently selected component
});

/**
 * Creates a new report object with default properties
 * @param {string} id - Unique identifier for the report
 * @param {string} name - Name of the report
 * @param {string} userId - User ID (optional)
 * @returns {Object} A new report object
 */
export const createReport = (id, name = 'Untitled Report', userId = null) => ({
  id,
  name,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  pages: [createPage(`page-${Date.now()}`)],
  activePageIndex: 0,
  userId: userId, // Store the user ID for Firestore permissions
});

/**
 * Gets the dimensions of a page based on its size and orientation
 * @param {Object} page - The page object
 * @returns {Object} The width and height in mm
 */
export const getPageDimensions = (page) => {
  let dimensions;

  if (page.pageSize === 'custom') {
    dimensions = { ...page.customSize };
  } else {
    dimensions = { ...PAGE_SIZES[page.pageSize] };
  }

  // Swap width and height for landscape orientation
  if (page.orientation === ORIENTATIONS.LANDSCAPE) {
    return {
      width: dimensions.height,
      height: dimensions.width
    };
  }

  return dimensions;
};
