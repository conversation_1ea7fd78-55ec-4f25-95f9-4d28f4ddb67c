import * as types from './types';
import { createReport, createPage } from '../../models/ReportTypes';
import { removeImageData } from '../../utils/storageUtils';
import * as reportService from '../../services/reportService';
import { generateReportId, generateSlug } from '../../utils/urlUtils';
import { auth } from '../../config/firebase';

/**
 * Action to create a new report
 * @param {string} name - Report name
 * @param {boolean} setActive - Whether to set the report as active
 * @returns {Function} Thunk function
 */
export const createNewReport = (name = 'Untitled Report', setActive = false) => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // Get current user
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Generate a unique ID for the report
    const reportId = `report-${generateReportId(name)}`;

    // Create the report object
    const newReport = createReport(reportId, name, currentUser.uid);

    // Save to Firestore
    const savedReport = await reportService.createReport(newReport);

    // Update Redux state
    dispatch({ type: types.CREATE_REPORT, payload: savedReport });

    // Optionally set the new report as active
    if (setActive) {
      dispatch({ type: types.SET_ACTIVE_REPORT, payload: savedReport.id });
    }

    return savedReport;
  } catch (error) {
    dispatch(setError('Failed to create report: ' + error.message));
    console.error('Create report error:', error);
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to update a report
 * @param {string} reportId - Report ID
 * @param {Object} updates - Updates to apply
 * @returns {Function} Thunk function
 */
export const updateReport = (reportId, updates) => async (dispatch, getState) => {
  try {
    dispatch(setLoading(true));

    const { report } = getState();
    const existingReport = report.reports.find(r => r.id === reportId);
    if (!existingReport) return;

    const updatedReport = {
      ...existingReport,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    // Save to Firestore
    await reportService.updateReport(reportId, updatedReport);

    // Update Redux state
    dispatch({ type: types.UPDATE_REPORT, payload: updatedReport });

    return updatedReport;
  } catch (error) {
    dispatch(setError('Failed to update report: ' + error.message));
    console.error('Update report error:', error);
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to save a report to Firebase
 * @param {string} reportId - Report ID
 * @param {Object} reportData - Full report data
 * @returns {Function} Thunk function
 */
export const saveReport = (reportId, reportData) => async (dispatch, getState) => {
  try {
    dispatch(setLoading(true));

    // Get the current report from state
    const { report } = getState();
    const existingReport = report.reports.find(r => r.id === reportId);
    if (!existingReport) return;

    // Prepare report data for saving
    const reportToSave = {
      ...existingReport,
      updatedAt: new Date().toISOString()
    };

    // Save to Firebase
    await reportService.updateReport(reportId, reportToSave);

    // Update local state
    dispatch({
      type: types.UPDATE_REPORT,
      payload: reportToSave
    });

    // Show success message
    console.log('Report saved successfully');

    return reportToSave;
  } catch (error) {
    dispatch(setError('Failed to save report: ' + error.message));
    console.error('Save report error:', error);
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to delete a report
 * @param {string} reportId - Report ID
 * @returns {Function} Thunk function
 */
export const deleteReport = (reportId) => async (dispatch, getState) => {
  try {
    dispatch(setLoading(true));

    const { report } = getState();
    const existingReport = report.reports.find(r => r.id === reportId);

    if (existingReport) {
      // Collect all image component IDs from the report
      const imageComponentIds = [];
      existingReport.pages.forEach(page => {
        page.components.forEach(component => {
          if (component.type === 'image') {
            imageComponentIds.push(component.id);
          }
        });
      });

      // Delete from Firestore
      await reportService.deleteReport(reportId);

      // Delete the report from Redux state
      dispatch({ type: types.DELETE_REPORT, payload: reportId });

      // Clean up image data
      imageComponentIds.forEach(id => removeImageData(id));
    } else {
      dispatch({ type: types.DELETE_REPORT, payload: reportId });
    }

    return true;
  } catch (error) {
    dispatch(setError('Failed to delete report: ' + error.message));
    console.error('Delete report error:', error);
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to set the active report
 * @param {string} reportId - Report ID
 * @returns {Object} Action object
 */
export const setActiveReport = (reportId) => ({
  type: types.SET_ACTIVE_REPORT,
  payload: reportId
});

/**
 * Action to add a page to a report
 * @param {string} reportId - Report ID
 * @param {number} afterPageIndex - Index after which to add the page
 * @returns {Function} Thunk function
 */
export const addPage = (reportId, afterPageIndex) => (dispatch) => {
  const newPage = createPage(`page-${Date.now()}`);
  dispatch({
    type: types.ADD_PAGE,
    payload: {
      reportId,
      page: newPage,
      afterPageIndex // If provided, insert after this index
    }
  });
  return newPage;
};

/**
 * Action to update a page
 * @param {string} reportId - Report ID
 * @param {string} pageId - Page ID
 * @param {Object} updates - Updates to apply
 * @returns {Object} Action object
 */
export const updatePage = (reportId, pageId, updates) => ({
  type: types.UPDATE_PAGE,
  payload: { reportId, pageId, updates }
});

/**
 * Action to delete a page
 * @param {string} reportId - Report ID
 * @param {string} pageId - Page ID
 * @returns {Function} Thunk function
 */
export const deletePage = (reportId, pageId) => (dispatch, getState) => {
  const { report } = getState();
  const existingReport = report.reports.find(r => r.id === reportId);

  // Prevent deleting the last page
  if (existingReport && existingReport.pages.length > 1) {
    // Find the page to get its image components before deletion
    const page = existingReport.pages.find(p => p.id === pageId);
    if (page) {
      // Collect all image component IDs from the page
      const imageComponentIds = page.components
        .filter(component => component.type === 'image')
        .map(component => component.id);

      // Delete the page
      dispatch({ type: types.DELETE_PAGE, payload: { reportId, pageId } });

      // Clean up image data
      imageComponentIds.forEach(id => removeImageData(id));
      return true;
    }

    dispatch({ type: types.DELETE_PAGE, payload: { reportId, pageId } });
    return true;
  }
  return false;
};

/**
 * Action to set the active page
 * @param {string} reportId - Report ID
 * @param {number} pageIndex - Page index
 * @returns {Object} Action object
 */
export const setActivePage = (reportId, pageIndex) => ({
  type: types.SET_ACTIVE_PAGE,
  payload: { reportId, pageIndex }
});

/**
 * Action to add a component to a page
 * @param {string} reportId - Report ID
 * @param {string} pageId - Page ID
 * @param {Object} component - Component to add
 * @returns {Function} Thunk function
 */
export const addComponent = (reportId, pageId, component) => (dispatch) => {
  dispatch({
    type: types.ADD_COMPONENT,
    payload: { reportId, pageId, component }
  });
  return component;
};

/**
 * Action to update a component
 * @param {string} reportId - Report ID
 * @param {string} pageId - Page ID
 * @param {string} componentId - Component ID
 * @param {Object} updates - Updates to apply
 * @returns {Object} Action object
 */
export const updateComponent = (reportId, pageId, componentId, updates) => ({
  type: types.UPDATE_COMPONENT,
  payload: { reportId, pageId, componentId, updates }
});

/**
 * Action to delete a component
 * @param {string} reportId - Report ID
 * @param {string} pageId - Page ID
 * @param {string} componentId - Component ID
 * @returns {Function} Thunk function
 */
export const deleteComponent = (reportId, pageId, componentId) => (dispatch, getState) => {
  const { report } = getState();
  const existingReport = report.reports.find(r => r.id === reportId);

  if (existingReport) {
    const page = existingReport.pages.find(p => p.id === pageId);
    if (page) {
      const component = page.components.find(c => c.id === componentId);
      if (component && component.type === 'image') {
        // Clean up image data after dispatching the delete action
        removeImageData(componentId);
      }
    }
  }

  dispatch({
    type: types.DELETE_COMPONENT,
    payload: { reportId, pageId, componentId }
  });
};

/**
 * Action to select a component
 * @param {string} reportId - Report ID
 * @param {string} pageId - Page ID
 * @param {string} componentId - Component ID
 * @returns {Object} Action object
 */
export const selectComponent = (reportId, pageId, componentId) => {
  console.log('Redux Action - selectComponent called with:', { reportId, pageId, componentId });
  return {
    type: types.SELECT_COMPONENT,
    payload: { reportId, pageId, componentId }
  };
};

/**
 * Action to set loading state
 * @param {boolean} isLoading - Loading state
 * @returns {Object} Action object
 */
export const setLoading = (isLoading) => ({
  type: types.SET_LOADING,
  payload: isLoading
});

/**
 * Action to set error state
 * @param {string} error - Error message
 * @returns {Object} Action object
 */
export const setError = (error) => ({
  type: types.SET_ERROR,
  payload: error
});

/**
 * Action to load reports from Firebase
 * @returns {Function} Thunk function
 */
export const loadReports = () => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // Load reports from Firestore
    const reports = await reportService.getAllReports();

    if (reports && reports.length > 0) {
      dispatch({
        type: types.LOAD_REPORTS,
        payload: reports
      });
    } else {
      // If no reports in Firestore, check localStorage as fallback
      const savedReports = localStorage.getItem('reports');
      if (savedReports) {
        const parsedReports = JSON.parse(savedReports);

        if (parsedReports.length > 0) {
          dispatch({
            type: types.LOAD_REPORTS,
            payload: parsedReports
          });

          // Save localStorage reports to Firestore for future use
          for (const report of parsedReports) {
            try {
              // Add user ID to the report
              const currentUser = auth.currentUser;
              if (currentUser) {
                const reportWithUser = {
                  ...report,
                  userId: currentUser.uid
                };
                await reportService.createReport(reportWithUser);
              }
            } catch (err) {
              console.error('Error migrating report to Firestore:', err);
            }
          }
        }
      }
    }

    return true;
  } catch (error) {
    dispatch(setError('Failed to load reports: ' + error.message));
    console.error('Load reports error:', error);
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to load a report by ID
 * @param {string} reportId - Report ID
 * @returns {Function} Thunk function
 */
export const loadReportById = (reportId) => async (dispatch, getState) => {
  try {
    console.log('loadReportById action called with ID:', reportId);

    // Always reset any previous error
    dispatch(setError(null));

    // Set loading state
    dispatch(setLoading(true));

    if (!reportId) {
      console.error('Invalid report ID provided:', reportId);
      throw new Error('Invalid report ID');
    }

    // Check if the report is already in the state
    const { report: reportState } = getState();

    // Try with and without the 'report-' prefix
    let existingReport = reportState.reports.find(r => r.id === reportId);

    if (!existingReport && reportId.startsWith('report-')) {
      // Try without prefix
      existingReport = reportState.reports.find(r => r.id === reportId.substring(7));
    } else if (!existingReport && !reportId.startsWith('report-')) {
      // Try with prefix
      existingReport = reportState.reports.find(r => r.id === `report-${reportId}`);
    }

    if (existingReport) {
      console.log('Report found in Redux state, using cached version:', existingReport.id);
      // Set as active report
      dispatch({ type: types.SET_ACTIVE_REPORT, payload: existingReport.id });

      // Make sure to reset loading state
      dispatch(setLoading(false));

      return existingReport;
    }

    console.log('Report not found in Redux state, fetching from Firestore:', reportId);

    // Load report from Firestore
    const report = await reportService.getReportById(reportId);

    if (report) {
      console.log('Report loaded from Firestore successfully:', report.id);
      // Add to Redux state
      dispatch({ type: types.CREATE_REPORT, payload: report });

      // Set as active report
      dispatch({ type: types.SET_ACTIVE_REPORT, payload: report.id });

      // Make sure to reset loading state
      dispatch(setLoading(false));

      return report;
    }

    console.error('Report not found in Firestore:', reportId);
    throw new Error('Report not found');
  } catch (error) {
    // Set error state
    dispatch(setError('Failed to load report: ' + error.message));
    console.error('Load report by ID error:', error);

    // Make sure to reset loading state
    dispatch(setLoading(false));

    throw error;
  }
};