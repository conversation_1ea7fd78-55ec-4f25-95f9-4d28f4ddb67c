import * as types from './types';
import * as authService from '../../services/authService';

/**
 * Action to set loading state
 * @param {boolean} isLoading - Loading state
 * @returns {Object} Action object
 */
export const setLoading = (isLoading) => ({
  type: types.AUTH_LOADING,
  payload: isLoading
});

/**
 * Action to set error state
 * @param {string} error - Error message
 * @returns {Object} Action object
 */
export const setError = (error) => ({
  type: types.AUTH_ERROR,
  payload: error
});

/**
 * Action to set user data
 * @param {Object} user - User data
 * @returns {Object} Action object
 */
export const setUser = (user) => ({
  type: types.SET_USER,
  payload: user
});

/**
 * Action to register a new user
 * @param {string} email - User email
 * @param {string} password - User password
 * @param {string} displayName - User display name
 * @returns {Function} Thunk function
 */
export const register = (email, password, displayName) => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    dispatch(setError(null));

    const user = await authService.register(email, password, displayName);

    dispatch(setUser({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || email.split('@')[0]
    }));

    return user;
  } catch (error) {
    dispatch(setError(error.message));
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to login a user with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Function} Thunk function
 */
export const login = (email, password) => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    dispatch(setError(null));

    const user = await authService.login(email, password);

    dispatch(setUser({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || email.split('@')[0]
    }));

    return user;
  } catch (error) {
    dispatch(setError(error.message));
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to login a user with Google
 * @returns {Function} Thunk function
 */
export const loginWithGoogle = () => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    dispatch(setError(null));

    const user = await authService.loginWithGoogle();

    dispatch(setUser({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || user.email.split('@')[0]
    }));

    return user;
  } catch (error) {
    dispatch(setError(error.message));
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to logout a user
 * @returns {Function} Thunk function
 */
export const logout = () => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    dispatch(setError(null));

    await authService.logout();

    dispatch({ type: types.LOGOUT });

  } catch (error) {
    dispatch(setError(error.message));
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to send password reset email
 * @param {string} email - User email
 * @returns {Function} Thunk function
 */
export const forgotPassword = (email) => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    dispatch(setError(null));

    await authService.forgotPassword(email);

  } catch (error) {
    dispatch(setError(error.message));
    throw error;
  } finally {
    dispatch(setLoading(false));
  }
};

/**
 * Action to check if user is authenticated
 * @returns {Function} Thunk function
 */
export const checkAuth = () => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    const user = await authService.getCurrentUser();

    if (user) {
      dispatch(setUser({
        uid: user.uid,
        email: user.email,
        displayName: user.displayName || user.email.split('@')[0]
      }));
    } else {
      dispatch({ type: types.LOGOUT });
    }

  } catch (error) {
    dispatch(setError(error.message));
    dispatch({ type: types.LOGOUT });
  } finally {
    dispatch(setLoading(false));
  }
};
