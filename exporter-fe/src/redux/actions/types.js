/**
 * Redux Action Types
 */

// Report actions
export const CREATE_REPORT = 'CREATE_REPORT';
export const UPDATE_REPORT = 'UPDATE_REPORT';
export const DELETE_REPORT = 'DELETE_REPORT';
export const SET_ACTIVE_REPORT = 'SET_ACTIVE_REPORT';
export const LOAD_REPORTS = 'LOAD_REPORTS';

// Page actions
export const ADD_PAGE = 'ADD_PAGE';
export const UPDATE_PAGE = 'UPDATE_PAGE';
export const DELETE_PAGE = 'DELETE_PAGE';
export const SET_ACTIVE_PAGE = 'SET_ACTIVE_PAGE';

// Component actions
export const ADD_COMPONENT = 'ADD_COMPONENT';
export const UPDATE_COMPONENT = 'UPDATE_COMPONENT';
export const DELETE_COMPONENT = 'DELETE_COMPONENT';
export const SELECT_COMPONENT = 'SELECT_COMPONENT';

// UI state actions
export const SET_LOADING = 'SET_LOADING';
export const SET_ERROR = 'SET_ERROR';

// Auth actions
export const AUTH_LOADING = 'AUTH_LOADING';
export const AUTH_ERROR = 'AUTH_ERROR';
export const SET_USER = 'SET_USER';
export const LOGOUT = 'LOGOUT';
