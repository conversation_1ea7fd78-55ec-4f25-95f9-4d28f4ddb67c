import * as types from '../actions/types';

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
};

/**
 * Auth reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
const authReducer = (state = initialState, action) => {
  switch (action.type) {
    case types.AUTH_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
      
    case types.AUTH_ERROR:
      return {
        ...state,
        error: action.payload
      };
      
    case types.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        error: null
      };
      
    case types.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        error: null
      };
      
    default:
      return state;
  }
};

export default authReducer;
