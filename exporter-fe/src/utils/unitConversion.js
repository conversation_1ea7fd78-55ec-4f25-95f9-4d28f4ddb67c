/**
 * Utility functions for unit conversion and zoom calculations
 */

// Constants for unit conversion
export const PIXELS_PER_INCH = 96; // Standard CSS pixel density
export const MM_PER_INCH = 25.4;   // Standard conversion (1 inch = 25.4 mm)

/**
 * Converts pixels to millimeters
 * @param {number} px - Value in pixels
 * @returns {number} Value in millimeters
 */
export const pxToMm = (px) => {
  return px * MM_PER_INCH / PIXELS_PER_INCH;
};

/**
 * Converts millimeters to pixels
 * @param {number} mm - Value in millimeters
 * @returns {number} Value in pixels
 */
export const mmToPx = (mm) => {
  return mm * PIXELS_PER_INCH / MM_PER_INCH;
};

/**
 * Calculates zoom level to fit the entire page
 * @param {Object} containerDimensions - Container dimensions in pixels
 * @param {Object} pageDimensions - Page dimensions in millimeters
 * @param {number} padding - Padding in pixels
 * @returns {number} Zoom level as a percentage
 */
export const calculateFitAllZoom = (containerDimensions, pageDimensions, padding = 10) => {
  if (!containerDimensions || !pageDimensions) return 100;

  // Get container dimensions
  const { width: containerWidthPx, height: containerHeightPx } = containerDimensions;

  // Calculate padding as a percentage of container size for more proportional results
  const horizontalPadding = Math.min(padding, containerWidthPx * 0.05);
  const verticalPadding = Math.min(padding, containerHeightPx * 0.05);

  // Account for padding
  const availableWidthPx = containerWidthPx - horizontalPadding * 2;
  const availableHeightPx = containerHeightPx - verticalPadding * 2;

  // Convert to millimeters
  const availableWidthMm = pxToMm(availableWidthPx);
  const availableHeightMm = pxToMm(availableHeightPx);

  // Calculate ratios
  const widthRatio = availableWidthMm / pageDimensions.width;
  const heightRatio = availableHeightMm / pageDimensions.height;

  // Use the smaller ratio to ensure the entire page fits
  const ratio = Math.min(widthRatio, heightRatio) * 100;
  // Apply a safety margin that's more conservative for smaller zoom levels
  const safetyFactor = ratio < 50 ? 0.95 : 0.95;
  return Math.floor(ratio * safetyFactor);
};

/**
 * Calculates zoom level to fit the page width
 * @param {Object} containerDimensions - Container dimensions in pixels
 * @param {Object} pageDimensions - Page dimensions in millimeters
 * @param {number} padding - Padding in pixels
 * @returns {number} Zoom level as a percentage
 */
export const calculateFitWidthZoom = (containerDimensions, pageDimensions, padding = 40) => {
  if (!containerDimensions || !pageDimensions) return 100;

  // Get container width
  const { width: containerWidthPx } = containerDimensions;

  // Calculate padding as a percentage of container size for more proportional results
  const horizontalPadding = Math.min(padding, containerWidthPx * 0.05);

  // Account for padding
  const availableWidthPx = containerWidthPx - horizontalPadding * 2;

  // Convert to millimeters
  const availableWidthMm = pxToMm(availableWidthPx);

  // Calculate ratio
  const ratio = (availableWidthMm / pageDimensions.width) * 100;

  // Apply a safety margin
  return Math.floor(ratio * 0.95);
};
