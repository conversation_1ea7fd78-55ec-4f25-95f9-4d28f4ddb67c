# Firebase Setup Instructions

To complete the Firebase setup for the frontend, you need to get the following values from your Firebase console:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `exporter-2ccaa`
3. Click on the gear icon (⚙️) next to "Project Overview" to access Project settings
4. In the "General" tab, scroll down to the "Your apps" section
5. If you don't see a Web app listed, click on the "</>" icon to add a new Web app
6. Register the app with a nickname (e.g., "exporter-web")
7. After registering, you'll see the Firebase configuration object that looks like this:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "exporter-2ccaa.firebaseapp.com",
  projectId: "exporter-2ccaa",
  storageBucket: "exporter-2ccaa.appspot.com",
  messagingSenderId: "XXXXXXXXXXXX",
  appId: "1:XXXXXXXXXXXX:web:XXXXXXXXXXXX",
  measurementId: "G-XXXXXXXXXX"
};
```

8. Copy the values from this configuration object to your `.env` file:

```
REACT_APP_FIREBASE_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
REACT_APP_FIREBASE_AUTH_DOMAIN=exporter-2ccaa.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=exporter-2ccaa
REACT_APP_FIREBASE_STORAGE_BUCKET=exporter-2ccaa.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=XXXXXXXXXXXX
REACT_APP_FIREBASE_APP_ID=1:XXXXXXXXXXXX:web:XXXXXXXXXXXX
REACT_APP_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX
```

9. Save the `.env` file

## Enable Authentication in Firebase

1. In the Firebase Console, go to "Authentication" in the left sidebar
2. Click on the "Get started" button
3. Enable the "Email/Password" sign-in method
4. Click "Save"

## Enable Firestore in Firebase

1. In the Firebase Console, go to "Firestore Database" in the left sidebar
2. Click on the "Create database" button
3. Choose "Start in production mode" and click "Next"
4. Select a location closest to your users and click "Enable"
5. Once Firestore is enabled, go to the "Rules" tab and update the rules to:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /reports/{reportId} {
      allow read, write: if request.auth != null && resource.data.userId == request.auth.uid;
    }
  }
}
```

6. Click "Publish"

## Running the Application

1. Start the backend server (if not already running):
   ```
   cd exporter-be
   npm run dev
   ```

2. Start the frontend development server:
   ```
   cd exporter-fe
   npm start
   ```

3. Open your browser and navigate to http://localhost:3000
