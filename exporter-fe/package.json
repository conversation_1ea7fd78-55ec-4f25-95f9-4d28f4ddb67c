{"name": "exporter-fe", "version": "0.1.0", "private": true, "dependencies": {"@lexical/react": "^0.31.0", "@tanstack/react-table": "^8.21.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "exporter-shared-utils": "file:../utils", "firebase": "^11.6.1", "lexical": "^0.31.0", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}