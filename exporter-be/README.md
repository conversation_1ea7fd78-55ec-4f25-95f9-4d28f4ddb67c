# Exporter Backend

This is the backend service for the Exporter application, providing authentication and state storage using Firebase.

## Features

- User authentication (signup, login, logout, password reset)
- Report state storage and retrieval
- Firebase integration for authentication and database

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- Firebase project

### Installation

1. Clone the repository
2. Navigate to the backend directory:
   ```
   cd exporter-be
   ```
3. Install dependencies:
   ```
   npm install
   ```
4. Create a `.env` file based on `.env.example` and fill in your Firebase credentials
5. Start the development server:
   ```
   npm run dev
   ```

### Firebase Setup

1. Create a new Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. Enable Authentication and Firestore in your Firebase project
3. Generate a new private key for your service account:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
4. Use the values from the JSON file to fill in your `.env` file

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `POST /api/auth/forgot-password` - Send password reset email
- `GET /api/auth/me` - Get current user information
- `POST /api/auth/logout` - Logout a user

### Reports

- `GET /api/reports` - Get all reports for the current user
- `GET /api/reports/:id` - Get a report by ID
- `POST /api/reports` - Create a new report
- `PUT /api/reports/:id` - Update a report
- `DELETE /api/reports/:id` - Delete a report

## Environment Variables

- `PORT` - Port for the server (default: 5000)
- `FIREBASE_PROJECT_ID` - Firebase project ID
- `FIREBASE_CLIENT_EMAIL` - Firebase client email
- `FIREBASE_PRIVATE_KEY` - Firebase private key
- `FIREBASE_DATABASE_URL` - Firebase database URL
- `JWT_SECRET` - Secret for JWT tokens
- `JWT_EXPIRES_IN` - JWT token expiration time
