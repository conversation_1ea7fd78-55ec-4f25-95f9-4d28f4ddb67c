const admin = require('firebase-admin');
const dotenv = require('dotenv');

dotenv.config();

// Check if we have the required environment variables
const requiredEnvVars = [
  'FIREBASE_PROJECT_ID',
  'FIREBASE_CLIENT_EMAIL',
  'FIREBASE_PRIVATE_KEY',
];

const missingEnvVars = requiredEnvVars.filter(
  (envVar) => !process.env[envVar]
);

if (missingEnvVars.length > 0) {
  console.error(
    `Missing required environment variables: ${missingEnvVars.join(', ')}`
  );
  console.error('Please check your .env file');
  process.exit(1);
}

// Initialize Firebase Admin SDK
try {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      // The private key needs to be properly formatted from the environment variable
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
    databaseURL: process.env.FIREBASE_DATABASE_URL,
  });

  // Configure Firestore settings
  const db = admin.firestore();
  db.settings({ timestampsInSnapshots: true });

  // Initialize Authentication
  const auth = admin.auth();

  console.log('Firebase Admin SDK initialized successfully');
  console.log('Firebase Authentication enabled');
  console.log('Firebase Firestore enabled');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Export the admin instance
module.exports = {
  admin,
  auth: admin.auth(),
  firestore: admin.firestore(),
};
