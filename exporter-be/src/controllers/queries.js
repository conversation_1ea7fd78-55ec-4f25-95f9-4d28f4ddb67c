const { firestore } = require('../config/firebase');
const { createQuery: createQueryObject, validateQuery } = require('exporter-shared-utils');

const getQueryResult = async(req, res) => {
    const jsonData = generateJsonData();

    return res.status(200).json({
        status: 'success',
        data: {
            queryResults: jsonData
        }
    });
}

const getQuery = async(req, res) => {
    try {
    console.log('createQuery called with body:', req.body, "by ", req.user);
    const { uid } = req.user;
    const {queryId} = req.params;
    const queryDoc = await firestore.collection('queries').doc(queryId).get();

    if (!queryDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Query not found'
      });
    }

    const queryData = queryDoc.data();

    // Check if the query belongs to the user
    if (queryData.userId !== uid) {
      return res.status(403).json({
        status: 'error',
        message: 'You do not have permission to access this query'
      });
    }

    return res.status(200).json({
      status: 'success',
      data: {
        id: queryDoc.id,
    

    }});
}
    catch (error) {
        console.error('Error getting query:', error);
        return res.status(500).json({
          status: 'error',
          message: error.message || 'Failed to get query'
        });
      }

}
const createQuery = async (req, res) => {
  try {
    console.log('createQuery called with body:', req.body, "by ", req.user);
    const { uid } = req.user;
    const { title, query, description } = req.body;

    // Create query object using shared utility
    const queryObject = createQueryObject(null, title, query, description, uid);

    // Validate the query object
    const validation = validateQuery(queryObject);
    if (!validation.isValid) {
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Save to Firestore
    const queryRef = firestore.collection('queries').doc();
    const queryData = {
      ...queryObject,
      userId: uid, // Ensure userId is set
    };

    await queryRef.set(queryData);

    return res.status(201).json({
      status: 'success',
      message: 'Query created successfully',
      data: {
        id: queryRef.id,
        ...queryData
      }
    });
  } catch (error) {
    console.error('Error creating query:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to create query'
    });
  }
};


const executeQuery = async (req, res) => {
  /*


  We need a system that uses the query object, parses it into SQL, and then executes it against the warehouse. 

  */
};


function generateJsonData() {
    const data = [];
    const baseTimestamp = Date.now(); // Get a base timestamp to increment from

    for (let i = 1; i <= 200; i++) {
        const userId = i;
        // Increment timestamp slightly for each entry to ensure uniqueness
        const currentTimestamp = new Date(baseTimestamp + (i * 10)).toISOString();

        // Combine user_id and timestamp for a "hash" input
        const inputForHash = `${userId}-${currentTimestamp}`;

        // Simple non-cryptographic "hash" approximation:
        // Use a combination of a basic string hash and a random component
        // to get a 10-character string. This is not a cryptographically secure hash.
        let hash = 0;
        for (let j = 0; j < inputForHash.length; j++) {
            const char = inputForHash.charCodeAt(j);
            hash = ((hash << 5) - hash) + char;
            hash |= 0; // Convert to 32bit integer
        }
        // Convert the hash to a base36 string (alphanumeric) and truncate
        const value = (hash < 0 ? -hash : hash).toString(36).substring(0, 10);

        data.push({
            user_id: userId,
            currenttimestamp: currentTimestamp,
            value: value.padEnd(10, '0') // Ensure it's always 10 characters long
        });
    }

    return data;
}

// Example usage:


module.exports = {
  getQuery,
  getQueryResult,
  createQuery
};