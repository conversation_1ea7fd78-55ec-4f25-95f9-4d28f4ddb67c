const { auth, firestore } = require('../config/firebase');

/**
 * Register a new user
 */
const register = async (req, res) => {
  try {
    const { email, password, displayName } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Email and password are required'
      });
    }
    
    // Create user in Firebase Authentication
    const userRecord = await auth.createUser({
      email,
      password,
      displayName: displayName || email.split('@')[0],
      emailVerified: false,
    });
    
    // Create user document in Firestore
    await firestore.collection('users').doc(userRecord.uid).set({
      email: userRecord.email,
      displayName: userRecord.displayName,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    
    return res.status(201).json({
      status: 'success',
      message: 'User registered successfully',
      data: {
        uid: userRecord.uid,
        email: userRecord.email,
        displayName: userRecord.displayName,
      }
    });
  } catch (error) {
    console.error('Error registering user:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to register user'
    });
  }
};

/**
 * Login user
 * Note: Actual authentication happens on the client side with Firebase Auth
 * This endpoint is for server-side validation and additional logic
 */
const login = async (req, res) => {
  console.log('login from', req.body);
  try {
    const { idToken } = req.body;
    
    if (!idToken) {
      return res.status(400).json({
        status: 'error',
        message: 'ID token is required'
      });
    }
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);
    
    // Get user data from Firestore
    const userDoc = await firestore.collection('users').doc(decodedToken.uid).get();
    console.log('userDoc', userDoc);
    
    if (!userDoc.exists) {
      // Create user document if it doesn't exist (for users created outside this API)
      await firestore.collection('users').doc(decodedToken.uid).set({
        email: decodedToken.email,
        displayName: decodedToken.name || decodedToken.email.split('@')[0],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }
    
    return res.status(200).json({
      status: 'success',
      message: 'Login successful',
      data: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        displayName: decodedToken.name,
      }
    });
  } catch (error) {
    console.error('Error logging in:', error);
    return res.status(401).json({
      status: 'error',
      message: error.message || 'Authentication failed'
    });
  }
};

/**
 * Get current user information
 */
const getUser = async (req, res) => {
  try {
    const { uid } = req.user;
    
    // Get user data from Firestore
    const userDoc = await firestore.collection('users').doc(uid).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }
    
    const userData = userDoc.data();
    
    return res.status(200).json({
      status: 'success',
      data: {
        uid,
        email: userData.email,
        displayName: userData.displayName,
      }
    });
  } catch (error) {
    console.error('Error getting user:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to get user information'
    });
  }
};

/**
 * Forgot password
 */
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        status: 'error',
        message: 'Email is required'
      });
    }
    
    // Send password reset email
    await auth.generatePasswordResetLink(email);
    
    return res.status(200).json({
      status: 'success',
      message: 'Password reset email sent'
    });
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to send password reset email'
    });
  }
};

/**
 * Logout user
 * Note: Actual logout happens on the client side
 * This endpoint is for server-side cleanup
 */
const logout = async (req, res) => {
  try {
    // Firebase doesn't have a server-side logout
    // This endpoint is for any server-side cleanup needed
    
    return res.status(200).json({
      status: 'success',
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Error logging out:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to logout'
    });
  }
};

module.exports = {
  register,
  login,
  getUser,
  forgotPassword,
  logout
};
