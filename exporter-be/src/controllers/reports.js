const { firestore } = require('../config/firebase');

/**
 * Get all reports for the current user
 */
const getAllReports = async (req, res) => {
  try {
    const { uid } = req.user;
    
    // Get all reports for the user
    const reportsSnapshot = await firestore
      .collection('reports')
      .where('userId', '==', uid)
      .orderBy('updatedAt', 'desc')
      .get();
    
    const reports = [];
    reportsSnapshot.forEach(doc => {
      reports.push({
        id: doc.id,
        ...doc.data(),
      });
    });
    
    return res.status(200).json({
      status: 'success',
      data: reports
    });
  } catch (error) {
    console.error('Error getting reports:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to get reports'
    });
  }
};

/**
 * Get a report by ID
 */
const getReportById = async (req, res) => {
  try {
    const { uid } = req.user;
    const { id } = req.params;
    
    // Get the report
    const reportDoc = await firestore.collection('reports').doc(id).get();
    
    if (!reportDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Report not found'
      });
    }
    
    const reportData = reportDoc.data();
    
    // Check if the report belongs to the user
    if (reportData.userId !== uid) {
      return res.status(403).json({
        status: 'error',
        message: 'You do not have permission to access this report'
      });
    }
    
    return res.status(200).json({
      status: 'success',
      data: {
        id: reportDoc.id,
        ...reportData
      }
    });
  } catch (error) {
    console.error('Error getting report:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to get report'
    });
  }
};

/**
 * Create a new report
 */
const createReport = async (req, res) => {
  try {
    const { uid } = req.user;
    const { title, pages } = req.body;
    
    if (!title) {
      return res.status(400).json({
        status: 'error',
        message: 'Title is required'
      });
    }
    
    // Create the report
    const reportRef = firestore.collection('reports').doc();
    const reportData = {
      title,
      userId: uid,
      pages: pages || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    await reportRef.set(reportData);
    
    return res.status(201).json({
      status: 'success',
      message: 'Report created successfully',
      data: {
        id: reportRef.id,
        ...reportData
      }
    });
  } catch (error) {
    console.error('Error creating report:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to create report'
    });
  }
};

/**
 * Update a report
 */
const updateReport = async (req, res) => {
  try {
    const { uid } = req.user;
    const { id } = req.params;
    const { title, pages } = req.body;
    
    // Get the report
    const reportDoc = await firestore.collection('reports').doc(id).get();
    
    if (!reportDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Report not found'
      });
    }
    
    const reportData = reportDoc.data();
    
    // Check if the report belongs to the user
    if (reportData.userId !== uid) {
      return res.status(403).json({
        status: 'error',
        message: 'You do not have permission to update this report'
      });
    }
    
    // Update the report
    const updateData = {
      updatedAt: new Date().toISOString(),
    };
    
    if (title) {
      updateData.title = title;
    }
    
    if (pages) {
      updateData.pages = pages;
    }
    
    await firestore.collection('reports').doc(id).update(updateData);
    
    return res.status(200).json({
      status: 'success',
      message: 'Report updated successfully',
      data: {
        id,
        ...reportData,
        ...updateData
      }
    });
  } catch (error) {
    console.error('Error updating report:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to update report'
    });
  }
};

/**
 * Delete a report
 */
const deleteReport = async (req, res) => {
  try {
    const { uid } = req.user;
    const { id } = req.params;
    
    // Get the report
    const reportDoc = await firestore.collection('reports').doc(id).get();
    
    if (!reportDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Report not found'
      });
    }
    
    const reportData = reportDoc.data();
    
    // Check if the report belongs to the user
    if (reportData.userId !== uid) {
      return res.status(403).json({
        status: 'error',
        message: 'You do not have permission to delete this report'
      });
    }
    
    // Delete the report
    await firestore.collection('reports').doc(id).delete();
    
    return res.status(200).json({
      status: 'success',
      message: 'Report deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting report:', error);
    return res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to delete report'
    });
  }
};

module.exports = {
  getAllReports,
  getReportById,
  createReport,
  updateReport,
  deleteReport
};
