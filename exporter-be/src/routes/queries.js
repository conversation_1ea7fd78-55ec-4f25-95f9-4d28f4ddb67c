const express = require('express');
const router = express.Router();
const queryController = require('../controllers/queries');
const { verifyToken } = require('../middleware/auth');


// All routes require authentication
router.use(verifyToken);

router.get('/:queryId', queryController.getQuery);
router.get('/getQueryResult/:queryId', queryController.getQueryResult);
router.post('/createQuery', queryController.createQuery);


module.exports = router;