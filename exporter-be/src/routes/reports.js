const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reports');
const { verifyToken } = require('../middleware/auth');


// All routes require authentication
router.use(verifyToken);


// Report routes
router.get('/', reportController.getAllReports);
router.get('/:id', reportController.getReportById);
router.post('/', reportController.createReport);
router.put('/:id', reportController.updateReport);
router.delete('/:id', reportController.deleteReport);


module.exports = router;
