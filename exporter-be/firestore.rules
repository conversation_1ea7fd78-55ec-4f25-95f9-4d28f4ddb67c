rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow users to read and write their own reports
    match /reports/{reportId} {
      // Allow reading existing reports
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      
      // Allow updating and deleting existing reports
      allow update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
      
      // Allow creating new reports
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
    }
  }
}
