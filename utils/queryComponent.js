/**
 * Query Types and Utilities
 * Shared utilities for query objects that can be used across frontend and backend
 */

/**
 * Creates a new query object with default properties
 * @param {string} id - Unique identifier for the query (optional, will be generated if not provided)
 * @param {string} title - Title of the query
 * @param {string} sqlQuery - The SQL query string
 * @param {string} description - Description of the query (optional)
 * @param {string} userId - User ID (optional)
 * @returns {Object} A new query object
 */
const createQuery = (id = null, title = 'Untitled Query', sqlQuery = '', description = '', userId = null) => ({
  id,
  title,
  query: sqlQuery,
  description,
  userId,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

/**
 * Validates a query object
 * @param {Object} queryObj - Query object to validate
 * @returns {Object} Validation result with isValid boolean and errors array
 */
const validateQuery = (queryObj) => {
  const errors = [];

  if (!queryObj.title || queryObj.title.trim() === '') {
    errors.push('Title is required');
  }

  if (queryObj.title && queryObj.title.length > 100) {
    errors.push('Title must be 100 characters or less');
  }

  if (queryObj.description && queryObj.description.length > 500) {
    errors.push('Description must be 500 characters or less');
  }

  // Basic SQL validation - just check if it's not empty and contains some SQL keywords
  if (!queryObj.query || queryObj.query.trim() === '') {
    errors.push('SQL query is required');
  } else {
    const sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'WITH'];
    const hasValidKeyword = sqlKeywords.some(keyword =>
      queryObj.query.toUpperCase().includes(keyword)
    );
    if (!hasValidKeyword) {
      errors.push('Query must contain valid SQL keywords (SELECT, INSERT, UPDATE, DELETE, WITH)');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Updates a query object with new data
 * @param {Object} existingQuery - Existing query object
 * @param {Object} updates - Updates to apply
 * @returns {Object} Updated query object
 */
const updateQuery = (existingQuery, updates) => ({
  ...existingQuery,
  ...updates,
  updatedAt: new Date().toISOString(),
});

/**
 * Generates a unique query ID based on title and timestamp
 * @param {string} title - Query title
 * @returns {string} Generated query ID
 */
const generateQueryId = (title) => {
  const timestamp = Date.now();
  const cleanTitle = title
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .substring(0, 30); // Limit length

  return `query-${cleanTitle}-${timestamp}`;
};

/**
 * Query execution status constants
 */
const QUERY_STATUS = {
  IDLE: 'idle',
  RUNNING: 'running',
  SUCCESS: 'success',
  ERROR: 'error'
};

/**
 * Default query templates for common use cases
 */
const QUERY_TEMPLATES = {
  SELECT_ALL: {
    title: 'Select All Records',
    query: 'SELECT * FROM table_name LIMIT 100;',
    description: 'Basic query to select all records from a table'
  },
  COUNT_RECORDS: {
    title: 'Count Records',
    query: 'SELECT COUNT(*) as total_count FROM table_name;',
    description: 'Count the total number of records in a table'
  },
  FILTERED_SELECT: {
    title: 'Filtered Select',
    query: 'SELECT * FROM table_name WHERE column_name = \'value\' LIMIT 100;',
    description: 'Select records with a specific filter condition'
  }
};

// Universal export for both CommonJS (Node.js) and ES modules (React)
module.exports = {
  createQuery,
  validateQuery,
  updateQuery,
  generateQueryId,
  QUERY_STATUS,
  QUERY_TEMPLATES
};