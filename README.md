# Exporter

A web application for creating and managing PDF reports with a React frontend and Firebase backend.

## Project Structure

- `exporter-fe`: Frontend React application
- `exporter-be`: Backend Node.js application with Firebase integration

## Features

- User authentication (signup, login, logout, password reset)
- Create and manage reports with multiple pages
- Drag and drop text and image components
- Customizable page sizes and layouts
- Export reports to PDF
- Cloud storage of reports using Firebase

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- Firebase account

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd exporter-fe
   ```
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example` and fill in your Firebase credentials
4. Start the development server:
   ```
   npm start
   ```

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd exporter-be
   ```
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example` and fill in your Firebase credentials
4. Start the development server:
   ```
   npm run dev
   ```

## Firebase Setup

1. Create a new Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. Enable Authentication and Firestore in your Firebase project
3. Generate a new private key for your service account:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
4. Use the values from the JSON file to fill in your `.env` files for both frontend and backend